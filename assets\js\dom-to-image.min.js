/*! dom-to-image 10-06-2017 */ !function(a){"use strict";function b(a,b){function c(a){return b.bgcolor&&(a.style.backgroundColor=b.bgcolor),b.width&&(a.style.width=b.width+"px"),b.height&&(a.style.height=b.height+"px"),b.style&&Object.keys(b.style).forEach(function(c){a.style[c]=b.style[c]}),a}return b=b||{},g(b),Promise.resolve(a).then(function(a){return i(a,b.filter,!0)}).then(j).then(k).then(c).then(function(c){return l(c,b.width||q.width(a),b.height||q.height(a))})}function c(a,b){return h(a,b||{}).then(function(b){return b.getContext("2d").getImageData(0,0,q.width(a),q.height(a)).data})}function d(a,b){return h(a,b||{}).then(function(a){return a.toDataURL()})}function e(a,b){return b=b||{},h(a,b).then(function(a){return a.toDataURL("image/jpeg",b.quality||1)})}function f(a,b){return h(a,b||{}).then(q.canvasToBlob)}function g(a){"undefined"==typeof a.imagePlaceholder?v.impl.options.imagePlaceholder=u.imagePlaceholder:v.impl.options.imagePlaceholder=a.imagePlaceholder,"undefined"==typeof a.cacheBust?v.impl.options.cacheBust=u.cacheBust:v.impl.options.cacheBust=a.cacheBust}function h(a,c){function d(a){var b=document.createElement("canvas");if(b.width=c.width||q.width(a),b.height=c.height||q.height(a),c.bgcolor){var d=b.getContext("2d");d.fillStyle=c.bgcolor,d.fillRect(0,0,b.width,b.height)}return b}return b(a,c).then(q.makeImage).then(q.delay(100)).then(function(b){var c=d(a);return c.getContext("2d").drawImage(b,0,0),c})}function i(a,b,c){function d(a){return a instanceof HTMLCanvasElement?q.makeImage(a.toDataURL()):a.cloneNode(!1)}function e(a,b,c){function d(a,b,c){var d=Promise.resolve();return b.forEach(function(b){d=d.then(function(){return i(b,c)}).then(function(b){b&&a.appendChild(b)})}),d}var e=a.childNodes;return 0===e.length?Promise.resolve(b):d(b,q.asArray(e),c).then(function(){return b})}function f(a,b){function c(){function c(a,b){function c(a,b){q.asArray(a).forEach(function(c){b.setProperty(c,a.getPropertyValue(c),a.getPropertyPriority(c))})}a.cssText?b.cssText=a.cssText:c(a,b)}c(window.getComputedStyle(a),b.style)}function d(){function c(c){function d(a,b,c){function d(a){var b=a.getPropertyValue("content");return a.cssText+" content: "+b+";"}function e(a){function b(b){return b+": "+a.getPropertyValue(b)+(a.getPropertyPriority(b)?" !important":"")}return q.asArray(a).map(b).join("; ")+";"}var f="."+a+":"+b,g=c.cssText?d(c):e(c);return document.createTextNode(f+"{"+g+"}")}var e=window.getComputedStyle(a,c),f=e.getPropertyValue("content");if(""!==f&&"none"!==f){var g=q.uid();b.className=b.className+" "+g;var h=document.createElement("style");h.appendChild(d(g,c,e)),b.appendChild(h)}}[":before",":after"].forEach(function(a){c(a)})}function e(){a instanceof HTMLTextAreaElement&&(b.innerHTML=a.value),a instanceof HTMLInputElement&&b.setAttribute("value",a.value)}function f(){b instanceof SVGElement&&(b.setAttribute("xmlns","http://www.w3.org/2000/svg"),b instanceof SVGRectElement&&["width","height"].forEach(function(a){var c=b.getAttribute(a);c&&b.style.setProperty(a,c)}))}return b instanceof Element?Promise.resolve().then(c).then(d).then(e).then(f).then(function(){return b}):b}return c||!b||b(a)?Promise.resolve(a).then(d).then(function(c){return e(a,c,b)}).then(function(b){return f(a,b)}):Promise.resolve()}function j(a){return s.resolveAll().then(function(b){var c=document.createElement("style");return a.appendChild(c),c.appendChild(document.createTextNode(b)),a})}function k(a){return t.inlineAll(a).then(function(){return a})}function l(a,b,c){return Promise.resolve(a).then(function(a){return a.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),(new XMLSerializer).serializeToString(a)}).then(q.escapeXhtml).then(function(a){return'<foreignObject x="0" y="0" width="100%" height="100%">'+a+"</foreignObject>"}).then(function(a){return'<svg xmlns="http://www.w3.org/2000/svg" width="'+b+'" height="'+c+'">'+a+"</svg>"}).then(function(a){return"data:image/svg+xml;charset=utf-8,"+a})}function m(){function a(){var a="application/font-woff",b="image/jpeg";return{woff:a,woff2:a,ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:b,jpeg:b,gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml"}}function b(a){var b=/\.([^\.\\/]*?)$/g.exec(a);return b?b[1]:""}function c(c){var d=b(c).toLowerCase();return a()[d]||""}function d(a){return a.search(/^(data:)/)!==-1}function e(a){return new Promise(function(b){for(var c=window.atob(a.toDataURL().split(",")[1]),d=c.length,e=new Uint8Array(d),f=0;f<d;f++)e[f]=c.charCodeAt(f);b(new Blob([e],{type:"image/png"}))})}function f(a){return a.toBlob?new Promise(function(b){a.toBlob(b)}):e(a)}function g(a,b){var c=document.implementation.createHTMLDocument(),d=c.createElement("base");c.head.appendChild(d);var e=c.createElement("a");return c.body.appendChild(e),d.href=b,e.href=a,e.href}function h(){var a=0;return function(){function b(){return("0000"+(Math.random()*Math.pow(36,4)<<0).toString(36)).slice(-4)}return"u"+b()+a++}}function i(a){return new Promise(function(b,c){var d=new Image;d.onload=function(){b(d)},d.onerror=c,d.src=a})}function j(a){var b=3e4;return v.impl.options.cacheBust&&(a+=(/\?/.test(a)?"&":"?")+(new Date).getTime()),new Promise(function(c){function d(){if(4===g.readyState){if(200!==g.status)return void(h?c(h):f("cannot fetch resource: "+a+", status: "+g.status));var b=new FileReader;b.onloadend=function(){var a=b.result.split(/,/)[1];c(a)},b.readAsDataURL(g.response)}}function e(){h?c(h):f("timeout of "+b+"ms occured while fetching resource: "+a)}function f(a){console.error(a),c("")}var g=new XMLHttpRequest;g.onreadystatechange=d,g.ontimeout=e,g.responseType="blob",g.timeout=b,g.open("GET",a,!0),g.send();var h=v.impl.options.imagePlaceholder;if(h){var i=h.split(/,/);i&&i[1]&&c(i[1])}})}function k(a,b){return"data:"+b+";base64,"+a}function l(a){return a.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1")}function m(a){return function(b){return new Promise(function(c){setTimeout(function(){c(b)},a)})}}function n(a){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}function o(a){return a.replace(/#/g,"%23").replace(/\n/g,"%0A")}function p(a){var b=r(a,"border-left-width"),c=r(a,"border-right-width");return a.scrollWidth+b+c}function q(a){var b=r(a,"border-top-width"),c=r(a,"border-bottom-width");return a.scrollHeight+b+c}function r(a,b){var c=window.getComputedStyle(a).getPropertyValue(b);return parseFloat(c.replace("px",""))}return{toSvg:b,toPng:d,toJpeg:e,toBlob:f,toPixelData:c,impl:{fontFaces:s,images:t,util:q,inliner:u,options:{}}}}function n(a){function b(a){return a.search(e)!==-1}function c(a){for(var b,c=[];null!==(b=e.exec(a));)c.push(b[1]);return c.filter(function(a){return!q.isDataUrl(a)})}function d(a,b,c,d){function e(a){return new RegExp("(url\\(['\"]?)("+q.escape(a)+")(['\"]?\\))","g")}return Promise.resolve(b).then(function(a){return c?q.loadExternalImage(a):Promise.resolve()}).then(function(b){var f=c?q.toDataUrl(b,q.mimeType(a)):a;return d.replace(e(a),"$1"+f+"$3")})}var e=/url\(['"]?([^'"]+?)['"]?\)/g;return{inlineAll:function(e){function f(){return Promise.all(c(e).map(function(a){return d(a,a,b(a),e)}))}return b(e)?f().then(function(a){return a.length?f():e}):Promise.resolve(e)}}}function o(a,b,c){function d(a){var b=a.match(/^data:([^;]+)/);return b?b[1]:""}function e(a){return a.search(/^data:/)!==-1}function f(a){return new Promise(function(b){for(var c=window.atob(a),d=c.length,e=new Uint8Array(d),f=0;f<d;f++)e[f]=c.charCodeAt(f);b(new Blob([e],{type:d(a)}))})}function g(a){return a.toDataURL()}function h(a,b){return"data:"+b+";base64,"+a}return{toDataUrl:e?g:h,mimeType:d,toBlob:f,isDataUrl:e,loadExternalImage:function(a){return"string"==typeof a?q.loadExternalImage(a):Promise.resolve(a)}}}var p={toSvg:b,toPng:d,toJpeg:e,toBlob:f,toPixelData:c,impl:{fontFaces:s,images:t,util:q,inliner:u,options:{}}},q=function(){function a(a){var b=3e4;return v.impl.options.cacheBust&&(a+=(/\?/.test(a)?"&":"?")+(new Date).getTime()),new Promise(function(c){function d(){if(4===g.readyState){if(200!==g.status)return void(h?c(h):f("cannot fetch resource: "+a+", status: "+g.status));var b=new FileReader;b.onloadend=function(){var a=b.result.split(/,/)[1];c(a)},b.readAsDataURL(g.response)}}function e(){h?c(h):f("timeout of "+b+"ms occured while fetching resource: "+a)}function f(a){console.error(a),c("")}var g=new XMLHttpRequest;g.onreadystatechange=d,g.ontimeout=e,g.responseType="blob",g.timeout=b,g.open("GET",a,!0),g.send();var h=v.impl.options.imagePlaceholder;if(h){var i=h.split(/,/);i&&i[1]&&c(i[1])}})}function b(a){return new Promise(function(b,c){var d=new Image;d.onload=function(){b(d)},d.onerror=c,d.src=a})}function c(a){var b=document.implementation.createHTMLDocument(),c=b.createElement("base");b.head.appendChild(c);var d=b.createElement("a");return b.body.appendChild(d),c.href=a,d.href=a,d.href}function d(){var a=0;return function(){function b(){return("0000"+(Math.random()*Math.pow(36,4)<<0).toString(36)).slice(-4)}return"u"+b()+a++}}function e(a){return a.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1")}function f(a){return function(b){return new Promise(function(c){setTimeout(function(){c(b)},a)})}}function g(a){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}function h(a){return a.replace(/#/g,"%23").replace(/\n/g,"%0A")}function i(a){var b=j(a,"border-left-width"),c=j(a,"border-right-width");return a.scrollWidth+b+c}function j(a,b){var c=window.getComputedStyle(a).getPropertyValue(b);return parseFloat(c.replace("px",""))}function k(a){var b=j(a,"border-top-width"),c=j(a,"border-bottom-width");return a.scrollHeight+b+c}function l(a){return a.toBlob?new Promise(function(b){a.toBlob(b)}):m(a)}function m(a){return new Promise(function(b){for(var c=window.atob(a.toDataURL().split(",")[1]),d=c.length,e=new Uint8Array(d),f=0;f<d;f++)e[f]=c.charCodeAt(f);b(new Blob([e],{type:"image/png"}))})}return{escape:e,parseExtension:function(a){var b=/\.([^\.\\/]*?)$/g.exec(a);return b?b[1]:""},mimeType:function(a){var b=q.parseExtension(a).toLowerCase();return{woff:"application/font-woff",woff2:"application/font-woff",ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml"}[b]||""},dataAsUrl:function(a,b){return"data:"+b+";base64,"+a},isDataUrl:function(a){return a.search(/^(data:)/)!==-1},canvasToBlob:l,resolveUrl:c,getAndEncode:a,uid:d(),delay:f,asArray:g,escapeXhtml:h,makeImage:b,width:i,height:k}}(),r=function(){function a(a){return a.replace(d,"$1")}function b(a){return{resolve:function(b,c){var d=c||document;return d.querySelector(a)?Promise.resolve(d.querySelector(a)):Promise.reject()},resolveAll:function(b,c){var d=c||document,e=q.asArray(d.querySelectorAll(a));return e.length?Promise.resolve(e):Promise.reject()}}}function c(a,c){return function(d){return new Promise(function(e){function f(){var a=q.uid();d.addEventListener(a,function(b){e(b.detail),d.removeEventListener(a,arguments.callee,!1)},!1);var b=document.createEvent("CustomEvent");b.initCustomEvent(a,!1,!1,c),d.dispatchEvent(b)}setTimeout(f,a)})}}var d=/url\(['"]?([^'"]+?)['"]?\)/;return{inlineAll:function(d){function e(){return Promise.all(q.asArray(document.styleSheets).map(function(a){try{return q.asArray(a.cssRules||[])}catch(b){return console.log("Error while reading CSS rules from "+a.href,b.toString()),[]}}).reduce(function(a,b){return a.concat(b)},[]).filter(function(a){return a.type===CSSRule.FONT_FACE_RULE}).filter(function(a){return n.inlineAll(a.style.getPropertyValue("src")).then(function(b){a.style.removeProperty("src"),a.style.setProperty("src",b)})}))}return d?e().then(function(){return d}):Promise.resolve()}}}(),s=function(){function a(a){function b(a){return q.isDataUrl(a.src)?Promise.resolve():c(a.src,a.crossOrigin).then(function(b){return new Promise(function(c,d){a.onload=c,a.onerror=d,a.src=b})},function(){return Promise.resolve()})}return Promise.all(q.asArray(a.querySelectorAll("img")).map(b))}function b(a){function b(a){var b=a.style.getPropertyValue("background");return b?n.inlineAll(b).then(function(b){a.style.setProperty("background",b,a.style.getPropertyPriority("background"))}).then(function(){return a}):Promise.resolve(a)}return Promise.all(q.asArray(a.querySelectorAll("*")).map(b))}function c(a,b){function c(a,c){if(q.isDataUrl(a))return Promise.resolve(a);var d=b||"anonymous";return new Promise(function(b){var e=new XMLHttpRequest;e.onreadystatechange=function(){if(4===e.readyState){if(200!==e.status)return void console.error("cannot fetch resource: "+a+", status: "+e.status);var c=e.response,d=new FileReader;d.onloadend=function(){var a=d.result.split(/,/)[1];b("data:"+(c.type||"")+";base64,"+a)},d.readAsDataURL(c)}},e.ontimeout=function(){console.error("timeout of 30000ms occured while fetching resource: "+a)},e.responseType="blob",e.timeout=3e4,e.open("GET",a,!0),e.crossOrigin=d,e.send()})}return c(a,b)}return{inlineAll:function(c){return a(c).then(function(){return b(c)})}}},t=function(){var a=/^data:/;return{toDataUrl:function(a,b){return"data:"+b+";base64,"+a},mimeType:function(a){var b=/\.([^\.\\/]*?)$/g.exec(a);return b?{woff:"application/font-woff",woff2:"application/font-woff",ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml"}[b[1].toLowerCase()]||"":""},isDataUrl:function(b){return b.search(a)!==-1},canvasToBlob:function(a){return a.toBlob?new Promise(function(b){a.toBlob(b)}):u(a)}}},u=function(a){return new Promise(function(b){for(var c=window.atob(a.toDataURL().split(",")[1]),d=c.length,e=new Uint8Array(d),f=0;f<d;f++)e[f]=c.charCodeAt(f);b(new Blob([e],{type:"image/png"}))})},v=p;"undefined"!=typeof module?module.exports=v:"function"==typeof define&&define.amd?define(function(){return v}):a.domtoimage=v}(this);
