<?php
/**
 * 核心功能类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 核心功能类
 */
class WP_Esig_Core {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // WooCommerce钩子
        add_action('woocommerce_checkout_process', array($this, 'validate_signature'));
        add_action('woocommerce_checkout_order_processed', array($this, 'process_signature'), 10, 3);
        
        // AJAX钩子
        add_action('wp_ajax_save_signature', array($this, 'ajax_save_signature'));
        add_action('wp_ajax_nopriv_save_signature', array($this, 'ajax_save_signature'));
        add_action('wp_ajax_get_contract_content', array($this, 'ajax_get_contract_content'));
        add_action('wp_ajax_nopriv_get_contract_content', array($this, 'ajax_get_contract_content'));
        add_action('wp_ajax_save_cpf_data', array($this, 'ajax_save_cpf_data'));
        add_action('wp_ajax_nopriv_save_cpf_data', array($this, 'ajax_save_cpf_data'));
        add_action('wp_ajax_save_custom_email_data', array($this, 'ajax_save_custom_email_data'));
        add_action('wp_ajax_nopriv_save_custom_email_data', array($this, 'ajax_save_custom_email_data'));
        add_action('wp_ajax_save_signature_form_data', array($this, 'ajax_save_signature_form_data'));
        add_action('wp_ajax_nopriv_save_signature_form_data', array($this, 'ajax_save_signature_form_data'));
        add_action('wp_ajax_test_pdf_download', array($this, 'ajax_test_pdf_download'));
        add_action('wp_ajax_nopriv_test_pdf_download', array($this, 'ajax_test_pdf_download'));
        add_action('wp_ajax_send_contract_immediately', array($this, 'ajax_send_contract_immediately'));
        add_action('wp_ajax_nopriv_send_contract_immediately', array($this, 'ajax_send_contract_immediately'));
        add_action('wp_ajax_generate_pdf_from_image', array($this, 'ajax_generate_pdf_from_image'));
        add_action('wp_ajax_nopriv_generate_pdf_from_image', array($this, 'ajax_generate_pdf_from_image'));
        add_action('wp_ajax_wp_esig_fix_database', array($this, 'ajax_fix_database'));
    }
    
    /**
     * 检查是否启用签名功能
     */
    public function is_signature_enabled() {
        return get_option('wp_esig_enabled', 'yes') === 'yes';
    }
    
    /**
     * 检查是否需要签名
     */
    public function is_signature_required() {
        return get_option('wp_esig_signature_required', 'yes') === 'yes';
    }
    
    /**
     * 验证签名
     */
    public function validate_signature() {
        if (!$this->is_signature_enabled() || !$this->is_signature_required()) {
            return;
        }
        
        $signature_data = $this->get_signature_from_session();
        
        if (empty($signature_data)) {
            wc_add_notice(__('Por favor, complete a assinatura eletrônica antes de continuar com o checkout.', 'wp-electronic-signature'), 'error');
        }
    }
    
    /**
     * 处理签名
     */
    public function process_signature($order_id, $posted_data, $order) {
        if (!$this->is_signature_enabled()) {
            return;
        }
        
        $signature_data = $this->get_signature_from_session();
        
        if (empty($signature_data)) {
            return;
        }
        
        // 确保我们有有效的订单对象
        if (!$order) {
            $order = wc_get_order($order_id);
        }
        
        if (!$order) {
            return;
        }
        
        // 保存签名记录
        $signature_id = $this->save_signature_record($order_id, $signature_data);
        
        if ($signature_id) {
            // 触发签名完成事件
            do_action('wp_esig_signature_completed', $signature_id, $order);
            
            // 清理会话数据
            $this->clear_signature_session();
        }
    }
    
    /**
     * AJAX保存签名
     */
    public function ajax_save_signature() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_esig_signature_nonce')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }
        
        $signature_data = sanitize_text_field($_POST['signature_data']);
        
        if (empty($signature_data)) {
            wp_send_json_error(__('签名数据不能为空', 'wp-electronic-signature'));
        }
        
        // 保存到会话
        if (class_exists('WC') && WC()->session) {
            WC()->session->set('wp_esig_signature_data', $signature_data);
        }
        
        // 生成签名图片
        $image_path = $this->generate_signature_image($signature_data);
        
        if ($image_path) {
            wp_send_json_success(array(
                'message' => __('签名保存成功', 'wp-electronic-signature'),
                'image_url' => $this->get_signature_image_url($image_path)
            ));
        } else {
            wp_send_json_error(__('签名图片生成失败', 'wp-electronic-signature'));
        }
    }
    
    /**
     * AJAX保存CPF数据
     */
    public function ajax_save_cpf_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_esig_signature_nonce')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        $cpf = sanitize_text_field($_POST['cpf']);

        if (empty($cpf)) {
            wp_send_json_error(__('CPF不能为空', 'wp-electronic-signature'));
        }

        // 验证CPF格式
        $cpf_numbers = preg_replace('/\D/', '', $cpf);
        if (strlen($cpf_numbers) !== 11 || !$this->validate_cpf($cpf_numbers)) {
            wp_send_json_error(__('CPF格式无效', 'wp-electronic-signature'));
        }

        // 保存到会话
        if (class_exists('WC') && WC()->session) {
            WC()->session->set('wp_esig_cpf_data', $cpf);
        }

        wp_send_json_success(array(
            'message' => __('CPF保存成功', 'wp-electronic-signature'),
            'cpf' => $cpf
        ));
    }

    /**
     * AJAX保存自定义邮箱数据
     */
    public function ajax_save_custom_email_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_esig_signature_nonce')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        $custom_email = sanitize_email($_POST['custom_email']);

        if (empty($custom_email)) {
            wp_send_json_error(__('自定义邮箱不能为空', 'wp-electronic-signature'));
        }

        // 验证邮箱格式
        if (!is_email($custom_email)) {
            wp_send_json_error(__('邮箱格式无效', 'wp-electronic-signature'));
        }

        // 保存到会话
        if (class_exists('WC') && WC()->session) {
            WC()->session->set('wp_esig_custom_email', $custom_email);
        }

        wp_send_json_success(array(
            'message' => __('自定义邮箱保存成功', 'wp-electronic-signature'),
            'custom_email' => $custom_email
        ));
    }

    /**
     * 验证CPF
     */
    private function validate_cpf($cpf) {
        if (strlen($cpf) !== 11 || preg_match('/^(\d)\1{10}$/', $cpf)) {
            return false;
        }

        $sum = 0;
        for ($i = 0; $i < 9; $i++) {
            $sum += intval($cpf[$i]) * (10 - $i);
        }
        $remainder = ($sum * 10) % 11;
        if ($remainder === 10 || $remainder === 11) $remainder = 0;
        if ($remainder !== intval($cpf[9])) return false;

        $sum = 0;
        for ($i = 0; $i < 10; $i++) {
            $sum += intval($cpf[$i]) * (11 - $i);
        }
        $remainder = ($sum * 10) % 11;
        if ($remainder === 10 || $remainder === 11) $remainder = 0;
        return $remainder === intval($cpf[10]);
    }

    /**
     * AJAX保存签名表单数据（CPF和卖家邮箱）
     */
    public function ajax_save_signature_form_data() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_esig_signature_nonce')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        $cpf = isset($_POST['cpf']) ? sanitize_text_field($_POST['cpf']) : '';
        $seller_email = isset($_POST['seller_email']) ? sanitize_email($_POST['seller_email']) : '';

        // 验证CPF格式
        if (!empty($cpf)) {
            $cpf_clean = preg_replace('/\D/', '', $cpf);
            if (!$this->validate_cpf($cpf_clean)) {
                wp_send_json_error(__('CPF格式无效', 'wp-electronic-signature'));
                return;
            }
        }

        // 验证邮箱格式
        if (!empty($seller_email) && !is_email($seller_email)) {
            wp_send_json_error(__('邮箱格式无效', 'wp-electronic-signature'));
            return;
        }

        // 保存到会话
        if (class_exists('WC') && WC()->session) {
            if (!empty($cpf)) {
                WC()->session->set('wp_esig_cpf_data', $cpf);
            }
            if (!empty($seller_email)) {
                WC()->session->set('wp_esig_seller_email', $seller_email);
                // 同时更新全局设置
                update_option('wp_esig_seller_email', $seller_email);
            }
        }

        wp_send_json_success(array(
            'message' => __('表单数据保存成功', 'wp-electronic-signature'),
            'cpf' => $cpf,
            'seller_email' => $seller_email
        ));
    }

    /**
     * AJAX立即发送合同邮件
     */
    public function ajax_send_contract_immediately() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_esig_signature_nonce')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        // 获取表单数据
        $cpf = isset($_POST['cpf']) ? sanitize_text_field($_POST['cpf']) : '';
        $signature_data = isset($_POST['signature_data']) ? sanitize_text_field($_POST['signature_data']) : '';
        $buyer_email = isset($_POST['buyer_email']) ? sanitize_email($_POST['buyer_email']) : '';
        $contract_html = isset($_POST['contract_html']) ? wp_kses_post($_POST['contract_html']) : '';

        // 验证必要数据
        if (empty($signature_data)) {
            wp_send_json_error(__('签名数据不能为空', 'wp-electronic-signature'));
            return;
        }

        if (empty($cpf)) {
            wp_send_json_error(__('CPF不能为空', 'wp-electronic-signature'));
            return;
        }

        if (empty($contract_html)) {
            wp_send_json_error(__('合同内容不能为空', 'wp-electronic-signature'));
            return;
        }

        // 记录接收到的HTML内容信息
        error_log('WP Electronic Signature: 接收到的合同HTML长度: ' . strlen($contract_html));
        error_log('WP Electronic Signature: HTML内容是否包含签名: ' . (strpos($contract_html, 'data:image/') !== false ? '是' : '否'));

        // 添加到调试信息中
        $debug_info[] = '接收到的HTML长度: ' . strlen($contract_html);
        $debug_info[] = 'HTML是否包含base64图片: ' . (strpos($contract_html, 'data:image/') !== false ? '是' : '否');

        // 检查HTML中是否包含签名相关的元素
        if (strpos($contract_html, 'signature') !== false) {
            $debug_info[] = '✅ HTML包含signature相关内容';
        } else {
            $debug_info[] = '❌ HTML不包含signature相关内容';
        }

        // 检查是否包含img标签
        $img_count = preg_match_all('/<img[^>]*>/i', $contract_html);
        $debug_info[] = 'HTML中img标签数量: ' . $img_count;

        if (empty($buyer_email) || !is_email($buyer_email)) {
            wp_send_json_error(__('买家邮箱不能为空或格式无效', 'wp-electronic-signature'));
            return;
        }

        // 验证CPF格式
        $cpf_clean = preg_replace('/\D/', '', $cpf);
        if (!$this->validate_cpf($cpf_clean)) {
            wp_send_json_error(__('CPF格式无效', 'wp-electronic-signature'));
            return;
        }

        // 获取客户信息（从结账表单）
        $customer_data = $this->get_customer_data_from_checkout();
        if (!$customer_data) {
            error_log('WP Electronic Signature: 无法获取客户信息 - POST数据: ' . print_r($_POST, true));
            wp_send_json_error(__('无法获取客户信息', 'wp-electronic-signature'));
            return;
        }

        error_log('WP Electronic Signature: 客户数据获取成功: ' . print_r($customer_data, true));

        // 保存数据到会话
        if (class_exists('WC') && WC()->session) {
            WC()->session->set('wp_esig_signature_data', $signature_data);
            WC()->session->set('wp_esig_cpf_data', $cpf);
            WC()->session->set('wp_esig_custom_email', $buyer_email);
        }

        // 记录邮箱来源信息
        error_log('WP Electronic Signature: 邮箱信息对比');
        error_log('WP Electronic Signature: 买家自定义邮箱: ' . $buyer_email);
        error_log('WP Electronic Signature: 结账页面邮箱: ' . $customer_data['email']);
        error_log('WP Electronic Signature: 使用的邮箱: ' . $buyer_email);

        // 创建临时订单数据用于生成合同
        $temp_order_data = array(
            'customer_name' => $customer_data['name'],
            'customer_email' => $buyer_email, // 使用买家自定义的邮箱
            'customer_phone' => $customer_data['phone'],
            'customer_cpf' => $cpf,
            'signature_data' => $signature_data,
            'contract_html' => $contract_html // 添加用户实际看到的合同HTML内容
        );

        error_log('WP Electronic Signature: 准备发送邮件 - 临时订单数据: ' . print_r($temp_order_data, true));

        try {
            // 发送邮件
            $email_class = WP_Esig_Email::get_instance();
            $result = $email_class->send_immediate_contract_email($temp_order_data);

            error_log('WP Electronic Signature: 邮件发送结果: ' . ($result ? '成功' : '失败'));

            if ($result) {
                wp_send_json_success(array(
                    'message' => __('合同邮件发送成功', 'wp-electronic-signature')
                ));
            } else {
                // 提供更详细的错误信息
                $error_message = __('邮件发送失败。可能的原因：', 'wp-electronic-signature') . "\n";
                $error_message .= __('1. PDF生成失败（服务器资源不足或配置问题）', 'wp-electronic-signature') . "\n";
                $error_message .= __('2. 邮件服务器配置错误', 'wp-electronic-signature') . "\n";
                $error_message .= __('3. 网络连接问题', 'wp-electronic-signature') . "\n";
                $error_message .= __('请联系管理员检查服务器日志获取详细信息。', 'wp-electronic-signature');

                wp_send_json_error($error_message);
            }
        } catch (Exception $e) {
            error_log('WP Electronic Signature: AJAX处理异常: ' . $e->getMessage());
            error_log('WP Electronic Signature: 异常堆栈: ' . $e->getTraceAsString());
            error_log('WP Electronic Signature: 异常文件: ' . $e->getFile() . ':' . $e->getLine());

            // 根据异常类型提供不同的错误信息
            $error_message = __('处理过程中发生错误：', 'wp-electronic-signature') . $e->getMessage();
            if (strpos($e->getMessage(), 'memory') !== false) {
                $error_message = __('服务器内存不足，无法生成PDF。请联系管理员增加服务器内存限制。', 'wp-electronic-signature');
            } elseif (strpos($e->getMessage(), 'timeout') !== false) {
                $error_message = __('处理超时，请稍后重试。如果问题持续存在，请联系管理员。', 'wp-electronic-signature');
            }

            wp_send_json_error($error_message);
        } catch (Error $e) {
            error_log('WP Electronic Signature: AJAX处理致命错误: ' . $e->getMessage());
            error_log('WP Electronic Signature: 错误堆栈: ' . $e->getTraceAsString());
            error_log('WP Electronic Signature: 错误文件: ' . $e->getFile() . ':' . $e->getLine());
            error_log('WP Electronic Signature: 错误类型: ' . get_class($e));

            // 提供更友好的致命错误信息
            $error_message = __('系统发生严重错误，无法完成操作。', 'wp-electronic-signature') . "\n";
            $error_message .= __('错误详情已记录到服务器日志中。', 'wp-electronic-signature') . "\n";
            $error_message .= __('请联系技术支持并提供以下错误代码：', 'wp-electronic-signature') . ' ' . substr(md5($e->getMessage() . $e->getFile() . $e->getLine()), 0, 8);

            wp_send_json_error($error_message);
        }
    }

    /**
     * 从结账表单获取客户数据
     */
    private function get_customer_data_from_checkout() {
        // 尝试从POST数据获取（AJAX请求）
        if (isset($_POST['billing_first_name']) && isset($_POST['billing_email'])) {
            $first_name = sanitize_text_field($_POST['billing_first_name']);
            $last_name = sanitize_text_field($_POST['billing_last_name'] ?? '');
            $full_name = trim($first_name . ' ' . $last_name);

            return array(
                'name' => $full_name ?: $first_name,
                'email' => sanitize_email($_POST['billing_email']),
                'phone' => sanitize_text_field($_POST['billing_phone'] ?? '')
            );
        }

        // 尝试从JavaScript传递的数据获取
        if (isset($_POST['customer_name']) || isset($_POST['customer_email'])) {
            return array(
                'name' => sanitize_text_field($_POST['customer_name'] ?? ''),
                'email' => sanitize_email($_POST['customer_email'] ?? ''),
                'phone' => sanitize_text_field($_POST['customer_phone'] ?? '')
            );
        }

        // 尝试从会话获取
        if (class_exists('WC') && WC()->session) {
            $customer_data = WC()->session->get('wp_esig_customer_data');
            if ($customer_data && !empty($customer_data['email'])) {
                return $customer_data;
            }
        }

        // 尝试从当前用户获取
        if (is_user_logged_in()) {
            $user = wp_get_current_user();

            if (class_exists('WC_Customer')) {
                $customer = new WC_Customer($user->ID);
                $first_name = $customer->get_first_name();
                $last_name = $customer->get_last_name();
                $full_name = trim($first_name . ' ' . $last_name);

                return array(
                    'name' => $full_name ?: $user->display_name,
                    'email' => $customer->get_email() ?: $user->user_email,
                    'phone' => $customer->get_billing_phone()
                );
            } else {
                return array(
                    'name' => $user->display_name,
                    'email' => $user->user_email,
                    'phone' => ''
                );
            }
        }

        // 最后尝试从DOM获取（如果有的话）
        return array(
            'name' => 'Cliente',
            'email' => '',
            'phone' => ''
        );
    }

    /**
     * AJAX获取合同内容
     */
    public function ajax_get_contract_content() {
        // 记录调试信息
        error_log('WP Electronic Signature: AJAX get_contract_content 请求开始');

        // 验证nonce
        $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
        if (!wp_verify_nonce($nonce, 'wp_esig_signature_nonce')) {
            error_log('WP Electronic Signature: Nonce验证失败');
            wp_send_json_error(__('安全验证失败', 'wp-electronic-signature'));
            return;
        }

        error_log('WP Electronic Signature: Nonce验证成功');

        // 获取客户信息
        $customer_data = array(
            'name' => sanitize_text_field(isset($_POST['customer_name']) ? $_POST['customer_name'] : ''),
            'email' => sanitize_email(isset($_POST['customer_email']) ? $_POST['customer_email'] : ''),
            'phone' => sanitize_text_field(isset($_POST['customer_phone']) ? $_POST['customer_phone'] : '')
        );

        error_log('WP Electronic Signature: 客户数据 - ' . print_r($customer_data, true));

        // 直接从HTML文件获取合同内容，不依赖数据库
        $contract_content = $this->get_dynamic_contract_content($customer_data);

        if ($contract_content) {
            error_log('WP Electronic Signature: 合同内容获取成功，长度: ' . strlen($contract_content));

            // 添加调试信息到响应中
            $debug_info = array(
                'template_hash' => md5($contract_content),
                'content_length' => strlen($contract_content),
                'timestamp' => current_time('mysql'),
                'contains_vendedor' => strpos($contract_content, 'VENDEDOR') !== false,
                'contains_comprador' => strpos($contract_content, 'COMPRADOR') !== false
            );

            wp_send_json_success(array(
                'content' => $contract_content,
                'debug' => $debug_info,
                'message' => __('合同内容获取成功', 'wp-electronic-signature')
            ));
        } else {
            error_log('WP Electronic Signature: 合同内容获取失败');
            wp_send_json_error(__('合同内容获取失败，请检查合同模板设置', 'wp-electronic-signature'));
        }
    }
    
    /**
     * 获取动态合同内容
     */
    public function get_dynamic_contract_content($customer_data = array(), $order = null) {
        error_log('WP Electronic Signature: 开始获取动态合同内容');

        // 直接从模板文件读取内容，跳过数据库查询
        $template_file = WP_ESIG_PLUGIN_DIR . 'templates/default-contract.html';

        if (!file_exists($template_file)) {
            error_log('WP Electronic Signature: 模板文件不存在: ' . $template_file);
            return false;
        }

        $template_content = file_get_contents($template_file);

        if (empty($template_content)) {
            error_log('WP Electronic Signature: 模板文件内容为空');
            return false;
        }

        error_log('WP Electronic Signature: 从文件读取模板成功，内容长度: ' . strlen($template_content));

        // 替换变量
        $content = $this->replace_contract_variables($template_content, $customer_data, $order);

        error_log('WP Electronic Signature: 变量替换完成，最终内容长度: ' . strlen($content));

        return $content;
    }
    
    /**
     * 替换合同变量
     */
    private function replace_contract_variables($content, $customer_data = array(), $order = null) {
        // 获取订单信息
        $order_data = $this->get_order_data($order);

        // 合并客户数据和订单数据
        $variables = array_merge($customer_data, $order_data);
        
        // 替换变量
        foreach ($variables as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }
        
        // 替换签名日期
        $content = str_replace('{signature_date}', date_i18n('Y-m-d H:i:s'), $content);

        // 替换甲方乙方信息
        $party_data = $this->get_party_data();
        foreach ($party_data as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }

        // 替换CPF信息
        $cpf_data = $this->get_cpf_from_session();
        $content = str_replace('{customer_cpf}', $cpf_data ? $cpf_data : '[A ser preenchido]', $content);

        // 替换自定义邮件信息
        $custom_email_data = $this->get_custom_email_from_session();
        $content = str_replace('{custom_email}', $custom_email_data ? $custom_email_data : '[A ser preenchido]', $content);

        return $content;
    }
    
    /**
     * 获取订单数据
     */
    private function get_order_data($order = null) {
        $order_data = array(
            'order_number' => $this->get_order_number($order),
            'order_total' => $this->get_order_total($order),
            'order_date' => $this->get_order_date($order),
            'order_items' => $this->get_order_items($order)
        );

        return $order_data;
    }
    
    /**
     * 获取订单号
     */
    private function get_order_number($order = null) {
        // 如果传入了订单对象，直接使用
        if ($order) {
            return $order->get_order_number();
        }

        // 尝试从当前订单获取
        $order = $this->get_current_order();
        if ($order) {
            return $order->get_order_number();
        }

        // 生成临时订单号
        return 'TEMP-' . time();
    }
    
    /**
     * 获取订单总额
     */
    private function get_order_total($order = null) {
        // 如果传入了订单对象，直接使用
        if ($order) {
            return $this->format_brazilian_currency($order->get_total());
        }

        // 尝试从当前订单获取
        $order = $this->get_current_order();
        if ($order) {
            return $this->format_brazilian_currency($order->get_total());
        }

        // 从购物车获取
        if (WC()->cart && !WC()->cart->is_empty()) {
            return $this->format_brazilian_currency(WC()->cart->get_total('edit'));
        }

        return $this->format_brazilian_currency(0);
    }

    /**
     * 格式化巴西货币
     */
    private function format_brazilian_currency($amount) {
        // 巴西雷亚尔格式：R$ 1.234,56
        $formatted = number_format((float)$amount, 2, ',', '.');
        return 'R$ ' . $formatted;
    }
    
    /**
     * 获取订单日期
     */
    private function get_order_date($order = null) {
        // 如果传入了订单对象，直接使用
        if ($order) {
            return $order->get_date_created()->date_i18n('Y-m-d H:i:s');
        }

        // 尝试从当前订单获取
        $order = $this->get_current_order();
        if ($order) {
            return $order->get_date_created()->date_i18n('Y-m-d H:i:s');
        }

        // 返回当前日期
        return date_i18n('Y-m-d H:i:s');
    }
    
    /**
     * 获取订单商品
     */
    private function get_order_items($order = null) {
        // 如果传入了订单对象，直接使用
        if ($order) {
            $items = array();
            foreach ($order->get_items() as $item) {
                $items[] = $item->get_name() . ' x ' . $item->get_quantity();
            }
            return implode('<br>', $items);
        }

        // 尝试从当前订单获取
        $order = $this->get_current_order();
        if ($order) {
            $items = array();
            foreach ($order->get_items() as $item) {
                $items[] = $item->get_name() . ' x ' . $item->get_quantity();
            }
            return implode('<br>', $items);
        }

        // 从购物车获取
        if (WC()->cart && !WC()->cart->is_empty()) {
            $items = array();
            foreach (WC()->cart->get_cart() as $cart_item) {
                $product = $cart_item['data'];
                $items[] = $product->get_name() . ' x ' . $cart_item['quantity'];
            }
            return implode('<br>', $items);
        }

        return __('暂无商品', 'wp-electronic-signature');
    }
    
    /**
     * 获取当前订单
     */
    private function get_current_order() {
        // 尝试从会话获取订单ID
        if (WC()->session) {
            $order_id = WC()->session->get('wp_esig_pending_order_id');
            if ($order_id) {
                return wc_get_order($order_id);
            }
        }
        
        // 尝试从POST/GET参数获取
        $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : (isset($_GET['order_id']) ? intval($_GET['order_id']) : 0);
        if ($order_id) {
            return wc_get_order($order_id);
        }
        
        return null;
    }
    
    /**
     * 从会话获取签名数据
     */
    private function get_signature_from_session() {
        if (class_exists('WC') && WC()->session) {
            return WC()->session->get('wp_esig_signature_data');
        }
        return '';
    }

    /**
     * 从会话获取CPF数据
     */
    private function get_cpf_from_session() {
        if (class_exists('WC') && WC()->session) {
            return WC()->session->get('wp_esig_cpf_data');
        }
        return '';
    }

    /**
     * 从会话获取自定义邮件数据
     */
    private function get_custom_email_from_session() {
        if (class_exists('WC') && WC()->session) {
            return WC()->session->get('wp_esig_custom_email');
        }
        return '';
    }
    
    /**
     * 清理签名会话
     */
    private function clear_signature_session() {
        if (class_exists('WC') && WC()->session) {
            WC()->session->__unset('wp_esig_signature_data');
            WC()->session->__unset('wp_esig_cpf_data');
            WC()->session->__unset('wp_esig_custom_email');
            // 清理旧的键名（向后兼容）
            WC()->session->__unset('wp_esig_customer_cpf');
        }
    }
    
    /**
     * 保存签名记录
     */
    private function save_signature_record($order_id, $signature_data) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }
        
        // 生成签名图片
        $image_path = $this->generate_signature_image($signature_data);
        if (!$image_path) {
            return false;
        }
        
        $database = WP_Esig_Database::get_instance();
        
        return $database->save_signature(array(
            'order_id' => $order_id,
            'user_id' => $order->get_user_id(),
            'signature_data' => $signature_data,
            'signature_image' => $image_path
        ));
    }
    
    /**
     * 生成签名图片
     */
    private function generate_signature_image($signature_data) {
        // 解析base64数据
        if (strpos($signature_data, 'data:image/png;base64,') === 0) {
            $image_data = str_replace('data:image/png;base64,', '', $signature_data);
            $image_data = base64_decode($image_data);
            
            // 创建上传目录
            $upload_dir = wp_upload_dir();
            $signature_dir = $upload_dir['basedir'] . '/wp-esig-signatures/';
            
            if (!file_exists($signature_dir)) {
                wp_mkdir_p($signature_dir);
            }
            
            // 生成文件名
            $filename = 'signature_' . time() . '_' . wp_generate_password(8, false) . '.png';
            $file_path = $signature_dir . $filename;
            
            // 保存文件
            if (file_put_contents($file_path, $image_data)) {
                return $filename;
            }
        }
        
        return false;
    }
    
    /**
     * 获取签名图片URL
     */
    private function get_signature_image_url($filename) {
        $upload_dir = wp_upload_dir();
        return $upload_dir['baseurl'] . '/wp-esig-signatures/' . $filename;
    }

    /**
     * 创建默认模板（如果缺失）
     */
    private function create_default_template_if_missing() {
        error_log('WP Electronic Signature: 尝试创建默认合同模板');

        global $wpdb;
        $table_name = $wpdb->prefix . 'esig_contract_templates';

        // 获取默认模板内容
        $plugin_instance = WP_Electronic_Signature::get_instance();
        $default_content = $plugin_instance->get_default_contract_template();

        // 插入默认模板
        $result = $wpdb->insert(
            $table_name,
            array(
                'name' => __('默认合同模板', 'wp-electronic-signature'),
                'content' => $default_content,
                'content_hash' => md5($default_content),
                'is_default' => 1,
                'status' => 'active',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%d', '%s', '%s', '%s')
        );

        if ($result) {
            error_log('WP Electronic Signature: 默认合同模板创建成功，ID: ' . $wpdb->insert_id);
        } else {
            error_log('WP Electronic Signature: 默认合同模板创建失败: ' . $wpdb->last_error);
        }
    }

    /**
     * 获取甲方乙方数据
     */
    private function get_party_data() {
        $current_time = date_i18n('Y-m-d H:i:s');

        // 获取当前用户信息（乙方 - 买方）
        $current_user = wp_get_current_user();
        $customer_name = '';

        if ($current_user->ID) {
            $customer_name = $current_user->display_name;
        } else {
            // 尝试从结账表单获取
            if (isset($_POST['billing_first_name']) && isset($_POST['billing_last_name'])) {
                $customer_name = sanitize_text_field($_POST['billing_first_name']) . ' ' . sanitize_text_field($_POST['billing_last_name']);
            } elseif (WC()->customer) {
                $customer_name = WC()->customer->get_first_name() . ' ' . WC()->customer->get_last_name();
            }
        }

        // 获取商店信息（甲方 - 卖方）
        $store_name = get_bloginfo('name');
        if (empty($store_name)) {
            $store_name = __('商店', 'wp-electronic-signature');
        }

        return array(
            'party_a_name' => $store_name,
            'party_a_date' => $current_time,
            'party_b_name' => $customer_name,
            'party_b_date' => $current_time
        );
    }

    /**
     * 检查数据库表是否存在
     */
    private function check_database_tables() {
        global $wpdb;

        $templates_table = $wpdb->prefix . 'esig_contract_templates';
        $signatures_table = $wpdb->prefix . 'esig_signatures';

        $templates_exists = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'") == $templates_table;
        $signatures_exists = $wpdb->get_var("SHOW TABLES LIKE '$signatures_table'") == $signatures_table;

        error_log('WP Electronic Signature: 表检查结果 - 模板表: ' . ($templates_exists ? '存在' : '不存在') . ', 签名表: ' . ($signatures_exists ? '存在' : '不存在'));

        return $templates_exists && $signatures_exists;
    }

    /**
     * 创建缺失的数据库表
     */
    private function create_missing_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // 签名记录表
        $signatures_table = $wpdb->prefix . 'esig_signatures';
        $signatures_sql = "CREATE TABLE $signatures_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            signature_data longtext NOT NULL,
            signature_image varchar(255) NOT NULL,
            signed_at datetime NOT NULL,
            ip_address varchar(45) NOT NULL,
            user_agent text NOT NULL,
            status varchar(20) DEFAULT 'completed',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY user_id (user_id),
            KEY signed_at (signed_at)
        ) $charset_collate;";

        // 合同模板表
        $templates_table = $wpdb->prefix . 'esig_contract_templates';
        $templates_sql = "CREATE TABLE $templates_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            content longtext NOT NULL,
            variables text,
            is_default tinyint(1) DEFAULT 0,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY name (name),
            KEY is_default (is_default)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($signatures_sql);
        dbDelta($templates_sql);

        // 创建默认合同模板
        $this->create_default_template_if_missing();

        error_log('WP Electronic Signature: 缺失的数据库表已创建');
    }

    /**
     * 确保默认模板存在
     */
    private function ensure_default_template_exists() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_contract_templates';

        // 检查是否有默认模板
        $template_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE is_default = 1 AND status = 'active'");

        if ($template_count == 0) {
            error_log('WP Electronic Signature: 没有找到默认模板，创建新的默认模板');
            $this->create_default_template_if_missing();
        } else {
            error_log('WP Electronic Signature: 找到 ' . $template_count . ' 个默认模板');
        }
    }

    /**
     * AJAX修复数据库
     */
    public function ajax_fix_database() {
        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('权限不足', 'wp-electronic-signature'));
            return;
        }

        // 验证nonce
        $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
        if (!wp_verify_nonce($nonce, 'wp_esig_fix_database')) {
            wp_send_json_error(__('安全验证失败', 'wp-electronic-signature'));
            return;
        }

        global $wpdb;

        try {
            // 删除现有表（如果存在）
            $templates_table = $wpdb->prefix . 'esig_contract_templates';
            $signatures_table = $wpdb->prefix . 'esig_signatures';

            $wpdb->query("DROP TABLE IF EXISTS $templates_table");
            $wpdb->query("DROP TABLE IF EXISTS $signatures_table");

            // 重新创建表
            $this->create_missing_tables();

            // 验证表是否创建成功
            $templates_exists = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'") == $templates_table;
            $signatures_exists = $wpdb->get_var("SHOW TABLES LIKE '$signatures_table'") == $signatures_table;

            if ($templates_exists && $signatures_exists) {
                // 检查是否有默认模板
                $template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_default = 1");

                wp_send_json_success(array(
                    'message' => '数据库修复成功',
                    'templates_table' => $templates_exists ? '已创建' : '创建失败',
                    'signatures_table' => $signatures_exists ? '已创建' : '创建失败',
                    'default_template' => $template_count > 0 ? '已创建' : '创建失败'
                ));
            } else {
                wp_send_json_error('数据库表创建失败');
            }

        } catch (Exception $e) {
            error_log('WP Electronic Signature: 数据库修复失败 - ' . $e->getMessage());
            wp_send_json_error('修复过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * AJAX测试PDF下载
     */
    public function ajax_test_pdf_download() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_esig_signature_nonce')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        // 获取表单数据
        $cpf = isset($_POST['cpf']) ? sanitize_text_field($_POST['cpf']) : '';
        $signature_data = isset($_POST['signature_data']) ? sanitize_text_field($_POST['signature_data']) : '';
        $buyer_email = isset($_POST['buyer_email']) ? sanitize_email($_POST['buyer_email']) : '';
        $contract_html = isset($_POST['contract_html']) ? wp_kses_post($_POST['contract_html']) : '';

        // 验证必要数据
        if (empty($signature_data)) {
            wp_send_json_error(__('签名数据不能为空', 'wp-electronic-signature'));
            return;
        }

        if (empty($cpf)) {
            wp_send_json_error(__('CPF不能为空', 'wp-electronic-signature'));
            return;
        }

        if (empty($contract_html)) {
            wp_send_json_error(__('合同内容不能为空', 'wp-electronic-signature'));
            return;
        }

        // 记录测试信息
        error_log('WP Electronic Signature: 开始测试PDF生成');
        error_log('WP Electronic Signature: 合同HTML长度: ' . strlen($contract_html));
        error_log('WP Electronic Signature: 签名数据长度: ' . strlen($signature_data));

        // 构建测试数据
        $test_data = array(
            'customer_name' => isset($_POST['customer_name']) ? sanitize_text_field($_POST['customer_name']) : 'Test User',
            'customer_email' => $buyer_email,
            'customer_cpf' => $cpf,
            'signature_data' => $signature_data,
            'contract_html' => $contract_html,
            'customer_phone' => isset($_POST['customer_phone']) ? sanitize_text_field($_POST['customer_phone']) : ''
        );

        // 创建调试信息收集器
        $debug_info = array();

        try {
            // 生成测试PDF
            $pdf_generator = WP_Esig_PDF::get_instance();

            // 开始收集调试信息
            $debug_info[] = '开始生成测试PDF';
            $debug_info[] = '合同HTML长度: ' . strlen($contract_html);
            $debug_info[] = '签名数据长度: ' . strlen($signature_data);

            $pdf_path = $pdf_generator->generate_test_pdf_with_debug($test_data, $debug_info);

            if (!$pdf_path || !file_exists($pdf_path)) {
                $debug_info[] = '❌ PDF生成失败 - 文件不存在';
                wp_send_json_error(array(
                    'message' => 'PDF生成失败',
                    'debug_info' => $debug_info
                ));
                return;
            }

            // 验证PDF文件
            $file_size = filesize($pdf_path);
            $debug_info[] = 'PDF文件大小: ' . $file_size . ' 字节';

            if ($file_size < 100) {
                $debug_info[] = '❌ PDF文件过小，可能生成失败';
                wp_send_json_error(array(
                    'message' => 'PDF文件无效',
                    'debug_info' => $debug_info
                ));
                return;
            }

            // 创建下载URL
            $upload_dir = wp_upload_dir();
            $relative_path = str_replace($upload_dir['basedir'], '', $pdf_path);
            $download_url = $upload_dir['baseurl'] . $relative_path;
            $filename = basename($pdf_path);

            $debug_info[] = '✅ PDF生成成功: ' . $pdf_path;
            $debug_info[] = '下载URL: ' . $download_url;

            wp_send_json_success(array(
                'message' => '测试PDF生成成功',
                'download_url' => $download_url,
                'filename' => $filename,
                'file_size' => $file_size,
                'debug_info' => $debug_info
            ));

        } catch (Exception $e) {
            $debug_info[] = '❌ 异常: ' . $e->getMessage();
            $debug_info[] = '异常文件: ' . $e->getFile() . ':' . $e->getLine();
            wp_send_json_error(array(
                'message' => 'PDF生成异常: ' . $e->getMessage(),
                'debug_info' => $debug_info
            ));
        } catch (Error $e) {
            $debug_info[] = '❌ 致命错误: ' . $e->getMessage();
            $debug_info[] = '错误文件: ' . $e->getFile() . ':' . $e->getLine();
            wp_send_json_error(array(
                'message' => '系统致命错误: ' . $e->getMessage(),
                'debug_info' => $debug_info
            ));
        }
    }

    /**
     * AJAX生成PDF从图片
     */
    public function ajax_generate_pdf_from_image() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_esig_signature_nonce')) {
            wp_die(__('安全验证失败', 'wp-electronic-signature'));
        }

        try {
            error_log('WP Electronic Signature: 开始处理图片转PDF请求');

            // 获取请求数据
            $contract_image = $_POST['contract_image'];
            $customer_data = array(
                'cpf' => sanitize_text_field($_POST['cpf']),
                'buyer_email' => sanitize_email($_POST['buyer_email']),
                'customer_name' => sanitize_text_field($_POST['customer_name']),
                'customer_email' => sanitize_email($_POST['customer_email']),
                'customer_phone' => sanitize_text_field($_POST['customer_phone'])
            );

            error_log('WP Electronic Signature: 客户数据 - ' . print_r($customer_data, true));

            // 验证必要数据
            if (empty($contract_image)) {
                wp_send_json_error('合同图片数据为空');
                return;
            }

            if (empty($customer_data['customer_name'])) {
                wp_send_json_error('客户姓名为空');
                return;
            }

            // 调用PDF生成
            $pdf_generator = WP_Esig_PDF::get_instance();
            $result = $pdf_generator->generate_pdf_from_contract_image($contract_image, $customer_data);

            if ($result) {
                error_log('WP Electronic Signature: PDF生成成功 - ' . $result['file_path']);
                wp_send_json_success(array(
                    'message' => 'PDF生成成功',
                    'download_url' => $result['download_url'],
                    'file_path' => $result['file_path']
                ));
            } else {
                error_log('WP Electronic Signature: PDF生成失败');
                wp_send_json_error('PDF生成失败');
            }

        } catch (Exception $e) {
            error_log('WP Electronic Signature: PDF生成异常 - ' . $e->getMessage());
            wp_send_json_error('PDF生成异常: ' . $e->getMessage());
        }
    }


}
