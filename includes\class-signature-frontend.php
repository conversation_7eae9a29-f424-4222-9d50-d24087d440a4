<?php
/**
 * 前端功能类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 前端功能类
 */
class WP_Esig_Frontend {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 加载前端资源
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // 页面加载时弹窗签名
        add_action('wp_footer', array($this, 'add_signature_modal'));
    }
    
    /**
     * 加载前端脚本和样式
     */
    public function enqueue_scripts() {
        // 只在结账页面加载
        if (!is_checkout()) {
            return;
        }
        
        // 检查是否启用签名功能
        $core = WP_Esig_Core::get_instance();
        if (!$core->is_signature_enabled()) {
            return;
        }
        
        // 加载CSS
        wp_enqueue_style(
            'wp-esig-frontend',
            WP_ESIG_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            WP_ESIG_VERSION
        );

        // 加载SignaturePad库
        wp_enqueue_script(
            'signature-pad',
            WP_ESIG_PLUGIN_URL . 'assets/js/signature_pad.min.js',
            array(),
            '4.1.7',
            true
        );

        // 加载html2canvas库（本地文件）
        wp_enqueue_script(
            'html2canvas',
            WP_ESIG_PLUGIN_URL . 'assets/js/html2canvas.min.js',
            array(),
            '1.4.1',
            true
        );

        // 加载JavaScript
        wp_enqueue_script(
            'wp-esig-frontend',
            WP_ESIG_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery', 'signature-pad', 'html2canvas'),
            WP_ESIG_VERSION,
            true
        );
        
        // 本地化脚本
        wp_localize_script('wp-esig-frontend', 'wp_esig_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_esig_signature_nonce'),
            'signature_timing' => 'on_page_load',
            'messages' => array(
                'signature_required' => __('请先完成电子签名', 'wp-electronic-signature'),
                'signature_saved' => __('签名保存成功', 'wp-electronic-signature'),
                'signature_error' => __('签名保存失败', 'wp-electronic-signature'),
                'loading' => __('正在加载...', 'wp-electronic-signature')
            )
        ));
    }
    

    
    /**
     * 添加签名弹窗
     */
    public function add_signature_modal() {
        // 只在结账页面显示
        if (!is_checkout()) {
            return;
        }

        $core = WP_Esig_Core::get_instance();

        // 检查是否启用签名功能
        if (!$core->is_signature_enabled()) {
            return;
        }

        // 直接渲染签名弹窗
        $this->render_signature_modal();
    }
    
    /**
     * 渲染签名弹窗
     */
    private function render_signature_modal() {
        $title = get_option('wp_esig_signature_page_title', __('Por favor, assine o contrato', 'wp-electronic-signature'));
        ?>
        <div id="wp-esig-signature-modal" class="wp-esig-modal" style="display: none;">
            <div class="wp-esig-modal-content">
                <div class="wp-esig-modal-header">
                    <h3><?php echo esc_html($title); ?></h3>
                    <span class="wp-esig-modal-close">&times;</span>
                </div>
                <div class="wp-esig-modal-body">
                    <div id="wp-esig-modal-contract-content" class="wp-esig-contract-content">
                        <div class="wp-esig-loading">
                            <?php _e('Carregando conteúdo do contrato...', 'wp-electronic-signature'); ?>
                        </div>
                    </div>

                    <div class="wp-esig-modal-signature-area">
                        <h4><?php _e('Por favor, preencha os dados e assine abaixo', 'wp-electronic-signature'); ?></h4>

                        <!-- CPF输入字段 -->
                        <div class="wp-esig-cpf-section">
                            <label for="wp-esig-modal-cpf"><?php _e('CPF:', 'wp-electronic-signature'); ?></label>
                            <input type="text" id="wp-esig-modal-cpf" class="wp-esig-cpf-input" placeholder="000.000.000-00" maxlength="14" required>
                            <div id="wp-esig-cpf-error" class="wp-esig-error-message" style="display: none;"></div>
                        </div>

                        <!-- 信息提示 -->
                        <div class="wp-esig-info-section">
                            <p class="wp-esig-field-description"><?php _e('O contrato assinado será enviado por e-mail em formato PDF para você e para o vendedor.', 'wp-electronic-signature'); ?></p>

                            <!-- 买家邮箱编辑 -->
                            <div class="wp-esig-buyer-email-section">
                                <label for="wp-esig-modal-buyer-email"><?php _e('Seu e-mail para receber o PDF:', 'wp-electronic-signature'); ?></label>
                                <input type="email" id="wp-esig-modal-buyer-email" class="wp-esig-buyer-email-input" placeholder="<EMAIL>" required>
                                <div id="wp-esig-buyer-email-error" class="wp-esig-error-message" style="display: none;"></div>
                                <p class="wp-esig-field-description" style="color: #2c5aa0; font-weight: 600;">
                                    <?php _e('⚠️ IMPORTANTE: Verifique se o e-mail está correto! O contrato PDF será enviado para este endereço.', 'wp-electronic-signature'); ?>
                                </p>
                                <p class="wp-esig-field-description"><?php _e('Você pode alterar o e-mail acima se desejar receber o contrato em outro endereço.', 'wp-electronic-signature'); ?></p>
                            </div>
                        </div>

                        <!-- 签名区域 -->
                        <div class="wp-esig-signature-section">
                            <label><?php _e('Assinatura:', 'wp-electronic-signature'); ?></label>

                            <!-- 签名画板区域 -->
                            <div class="wp-esig-signature-pad-container">
                                <canvas id="wp-esig-modal-signature-pad" width="400" height="150"></canvas>
                            </div>

                            <div class="wp-esig-signature-controls" style="display: none;">
                                <button type="button" id="wp-esig-modal-clear-signature" class="button">
                                    <?php _e('Limpar', 'wp-electronic-signature'); ?>
                                </button>
                                <button type="button" id="wp-esig-modal-save-signature" class="button button-primary">
                                    <?php _e('Salvar', 'wp-electronic-signature'); ?>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="wp-esig-agreement">
                        <label>
                            <input type="checkbox" id="wp-esig-modal-agree-contract" required>
                            <?php _e('Li e concordo com todos os termos do contrato acima', 'wp-electronic-signature'); ?>
                        </label>
                    </div>

                    <div class="wp-esig-modal-actions">
                        <button type="button" id="wp-esig-modal-confirm" class="button button-primary" disabled>
                            <?php _e('Confirmar e continuar', 'wp-electronic-signature'); ?>
                        </button>
                        <button type="button" id="wp-esig-test-pdf-download" class="button" style="background: #007cba; color: white; margin-left: 10px;" disabled>
                            <?php _e('🔍 Testar PDF', 'wp-electronic-signature'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // DOM加载完成后2秒显示弹窗，不等待页面完全加载
            setTimeout(function() {
                $('#wp-esig-signature-modal').fadeIn(300);
                $('body').addClass('wp-esig-modal-open');

                // 禁用结账表单提交
                $('.woocommerce-checkout .button[type="submit"]').prop('disabled', true);

                // 初始化买家邮箱输入字段
                var customerEmail = $('#billing_email').val();
                if (customerEmail && customerEmail.trim() !== '') {
                    $('#wp-esig-modal-buyer-email').val(customerEmail);
                    console.log('自动填充买家邮箱:', customerEmail);

                    // 立即保存邮箱到会话
                    $.ajax({
                        url: wp_esig_ajax.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'save_custom_email_data',
                            nonce: wp_esig_ajax.nonce,
                            custom_email: customerEmail
                        },
                        success: function(response) {
                            if (response.success) {
                                console.log('初始化时自定义邮箱保存成功:', response.data.custom_email);
                            }
                        }
                    });

                    // 立即同步到合同显示 - 延迟执行确保WPEsigFrontend已加载
                    setTimeout(function() {
                        if (typeof WPEsigFrontend !== 'undefined' && WPEsigFrontend.updateContractCustomEmail) {
                            console.log('初始化时同步邮件到合同:', customerEmail);
                            WPEsigFrontend.updateContractCustomEmail(customerEmail);
                        } else {
                            console.log('初始化时WPEsigFrontend对象不存在，延迟同步');
                        }
                    }, 500);

                    // 添加视觉提示，让用户注意到邮箱字段
                    $('#wp-esig-modal-buyer-email').css({
                        'border': '2px solid #2c5aa0',
                        'background-color': '#f0f8ff'
                    });

                    // 3秒后恢复正常样式
                    setTimeout(function() {
                        $('#wp-esig-modal-buyer-email').css({
                            'border': '2px solid #b3d9f2',
                            'background-color': '#ffffff'
                        });
                    }, 3000);
                } else {
                    console.log('未检测到买家邮箱，等待用户输入');
                }

                // 监听结账页面邮箱字段变化，同步到弹窗
                $('#billing_email').on('input change', function() {
                    var newEmail = $(this).val();
                    if (newEmail && newEmail.trim() !== '') {
                        $('#wp-esig-modal-buyer-email').val(newEmail);
                    }
                });

                // 弹窗显示后重新初始化签名功能
                if (typeof WPEsigFrontend !== 'undefined') {
                    // 重新初始化弹窗签名画板
                    setTimeout(function() {
                        WPEsigFrontend.initModalSignaturePad();
                        console.log('弹窗签名功能已重新初始化');

                        // 重新加载合同内容
                        WPEsigFrontend.loadContractContent();
                        console.log('重新加载合同内容');

                        // 初始化验证状态
                        setTimeout(function() {
                            updateModalConfirmButton();
                        }, 100);
                    }, 500);
                }
            }, 2000); // 2秒后显示弹窗
            
            // 关闭弹窗
            $('.wp-esig-modal-close').on('click', function() {
                $('#wp-esig-signature-modal').fadeOut(300);
                $('body').removeClass('wp-esig-modal-open');
            });
            
            // 同意条款复选框
            $('#wp-esig-modal-agree-contract').on('change', function() {
                updateModalConfirmButton();
            });

            // 旧的HTML转PDF逻辑已移除，现在统一使用frontend.js中的图片转PDF逻辑

            // CPF输入验证
            $('#wp-esig-modal-cpf').on('input', function() {
                var cpf = $(this).val();
                // 自动格式化CPF
                cpf = cpf.replace(/\D/g, ''); // 移除非数字字符
                cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
                $(this).val(cpf);

                updateModalConfirmButton();
            });

            // 买家邮箱验证 - 已移至frontend.js中统一处理，避免重复绑定

            // 更新确认按钮状态的函数
            function updateModalConfirmButton() {
                var isChecked = $('#wp-esig-modal-agree-contract').is(':checked');

                // 检查签名是否完成（通过WPEsigFrontend对象检查画板状态）
                var hasSignature = false;
                if (typeof WPEsigFrontend !== 'undefined' && WPEsigFrontend.modalSignaturePad) {
                    hasSignature = !WPEsigFrontend.modalSignaturePad.isEmpty();
                }

                var cpfValue = $('#wp-esig-modal-cpf').val().replace(/\D/g, '');
                var hasValidCpf = cpfValue.length === 11 && isValidCPF(cpfValue);
                var buyerEmail = $('#wp-esig-modal-buyer-email').val();
                var hasValidBuyerEmail = isValidEmail(buyerEmail);

                var allValid = isChecked && hasSignature && hasValidCpf && hasValidBuyerEmail;
                $('#wp-esig-modal-confirm').prop('disabled', !allValid);

                // 测试PDF按钮只需要签名、CPF和邮箱即可（不需要同意条款）
                var testPdfValid = hasSignature && hasValidCpf && hasValidBuyerEmail;
                $('#wp-esig-test-pdf-download').prop('disabled', !testPdfValid);

                // 显示CPF错误信息
                if (cpfValue.length > 0 && !hasValidCpf) {
                    $('#wp-esig-cpf-error').text('CPF inválido').show();
                } else {
                    $('#wp-esig-cpf-error').hide();
                }

                // 显示买家邮箱错误信息
                if (buyerEmail.length > 0 && !hasValidBuyerEmail) {
                    $('#wp-esig-buyer-email-error').text('E-mail inválido').show();
                } else {
                    $('#wp-esig-buyer-email-error').hide();
                }

                // 显示综合错误提示
                showValidationErrors(hasSignature, hasValidCpf, hasValidBuyerEmail, isChecked);
            }

            // CPF验证函数
            function isValidCPF(cpf) {
                if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) return false;

                var sum = 0;
                for (var i = 0; i < 9; i++) {
                    sum += parseInt(cpf.charAt(i)) * (10 - i);
                }
                var remainder = (sum * 10) % 11;
                if (remainder === 10 || remainder === 11) remainder = 0;
                if (remainder !== parseInt(cpf.charAt(9))) return false;

                sum = 0;
                for (var i = 0; i < 10; i++) {
                    sum += parseInt(cpf.charAt(i)) * (11 - i);
                }
                remainder = (sum * 10) % 11;
                if (remainder === 10 || remainder === 11) remainder = 0;
                return remainder === parseInt(cpf.charAt(10));
            }

            // 邮箱验证函数
            function isValidEmail(email) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            // 显示综合验证错误提示
            function showValidationErrors(hasSignature, hasValidCpf, hasValidBuyerEmail, hasAgreed) {
                var errors = [];

                if (!hasSignature) {
                    errors.push('Assinatura');
                }
                if (!hasValidCpf) {
                    errors.push('CPF');
                }
                if (!hasValidBuyerEmail) {
                    errors.push('E-mail válido');
                }
                if (!hasAgreed) {
                    errors.push('Concordar com os termos');
                }

                // 移除之前的错误提示
                $('#wp-esig-general-error').remove();

                if (errors.length > 0) {
                    var errorMessage = 'Por favor, preencha os seguintes campos obrigatórios: ' + errors.join(', ');
                    var errorHtml = '<div id="wp-esig-general-error" class="wp-esig-error-message" style="color: #d63638; margin-top: 10px; padding: 8px; background: #ffeaea; border: 1px solid #d63638; border-radius: 4px;">' + errorMessage + '</div>';
                    $('#wp-esig-modal-confirm').after(errorHtml);
                }
            }

            // PDF下载按钮事件绑定已移至frontend.js中统一处理

            // 确认按钮 - 使用已测试成功的PDF生成方法
            $('#wp-esig-modal-confirm').on('click', function() {
                var $button = $(this);
                var originalText = $button.text();

                // 禁用按钮并显示加载状态
                $button.prop('disabled', true).text('Gerando PDF e enviando...');

                // 获取表单数据
                var cpfValue = $('#wp-esig-modal-cpf').val();
                var buyerEmail = $('#wp-esig-modal-buyer-email').val();

                // 获取签名数据
                var signatureData = '';
                if (typeof WPEsigFrontend !== 'undefined' && WPEsigFrontend.modalSignaturePad) {
                    signatureData = WPEsigFrontend.modalSignaturePad.toDataURL('image/png');
                }

                // 获取客户数据
                var customerData = {
                    billing_first_name: $('#billing_first_name').val() || $('input[name="billing_first_name"]').val() || '',
                    billing_last_name: $('#billing_last_name').val() || $('input[name="billing_last_name"]').val() || '',
                    billing_email: $('#billing_email').val() || $('input[name="billing_email"]').val() || '',
                    billing_phone: $('#billing_phone').val() || $('input[name="billing_phone"]').val() || ''
                };

                // 如果还是没有获取到邮箱，尝试其他方式
                if (!customerData.billing_email) {
                    customerData.billing_email = $('input[type="email"]').first().val() || '';
                }
                if (!customerData.billing_first_name) {
                    customerData.billing_first_name = 'Cliente';
                }

                console.log('获取到的客户数据:', customerData);

                // 验证必要数据
                if (!buyerEmail) {
                    alert('Por favor, preencha seu e-mail antes de continuar.');
                    $button.prop('disabled', false).text(originalText);
                    return;
                }

                if (!signatureData) {
                    alert('Por favor, faça sua assinatura antes de continuar.');
                    $button.prop('disabled', false).text(originalText);
                    return;
                }

                // 使用已测试成功的PDF生成方法，现在会自动发送邮件
                if (typeof WPEsigFrontend !== 'undefined') {
                    // 先捕获合同图片
                    WPEsigFrontend.captureContractImage()
                        .then(function(contractImageData) {
                            console.log('📤 准备发送PDF生成和邮件发送请求...');

                            // 调用修改后的generate_pdf_from_image接口（现在会自动发送邮件）
                            $.ajax({
                                url: wp_esig_ajax.ajax_url,
                                type: 'POST',
                                data: {
                                    action: 'generate_pdf_from_image',
                                    nonce: wp_esig_ajax.nonce,
                                    contract_image: contractImageData,
                                    cpf: cpfValue,
                                    buyer_email: buyerEmail,
                                    customer_name: (customerData.billing_first_name + ' ' + customerData.billing_last_name).trim() || 'Cliente',
                                    customer_email: buyerEmail,
                                    customer_phone: customerData.billing_phone || ''
                                },
                                success: function(response) {
                                    console.log('PDF生成和邮件发送响应:', response);

                                    if (response.success) {
                                        // 关闭弹窗
                                        $('#wp-esig-signature-modal').fadeOut(300);
                                        $('body').removeClass('wp-esig-modal-open');

                                        // 启用结账表单提交
                                        $('.woocommerce-checkout .button[type="submit"]').prop('disabled', false);

                                        // 显示成功消息
                                        if (response.data.email_sent) {
                                            alert('Contrato gerado e enviado com sucesso!');
                                        } else {
                                            alert('Contrato gerado com sucesso, mas houve problema no envio do e-mail. ' + (response.data.warning || ''));
                                        }
                                    } else {
                                        console.error('❌ PDF生成失败:', response.data);
                                        alert('Erro ao gerar contrato: ' + (response.data || 'Erro desconhecido'));
                                        $button.prop('disabled', false).text(originalText);
                                    }
                                },
                                error: function() {
                                    alert('Erro ao processar contrato. Tente novamente.');
                                    $button.prop('disabled', false).text(originalText);
                                }
                            });
                        })
                        .catch(function(error) {
                            console.error('❌ 合同图片捕获失败:', error);
                            alert('Erro ao capturar contrato. Tente novamente.');
                            $button.prop('disabled', false).text(originalText);
                        });
                } else {
                    alert('Erro: Sistema de assinatura não inicializado.');
                    $button.prop('disabled', false).text(originalText);
                }
            });

            // 显示合同发送成功通知
            function showContractSentNotification() {
                // 创建通知弹窗
                var notification = $('<div id="wp-esig-success-notification" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3); z-index: 10001; max-width: 400px; text-align: center; font-family: Arial, sans-serif;">' +
                    '<div style="color: #28a745; font-size: 48px; margin-bottom: 15px;">✓</div>' +
                    '<h3 style="color: #333; margin-bottom: 15px;">Contrato Enviado com Sucesso!</h3>' +
                    '<p style="color: #666; margin-bottom: 20px; line-height: 1.5;">O contrato assinado foi enviado para os e-mails informados.</p>' +
                    '<p style="color: #999; font-size: 14px; margin-bottom: 20px;"><strong>Importante:</strong> Verifique sua caixa de entrada. Se não encontrar o e-mail, verifique a pasta de spam/lixo eletrônico.</p>' +
                    '<button id="wp-esig-notification-close" style="background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 14px;">Entendi</button>' +
                '</div>');

                // 添加背景遮罩
                var overlay = $('<div id="wp-esig-notification-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000;"></div>');

                $('body').append(overlay).append(notification);

                // 关闭按钮事件
                $('#wp-esig-notification-close, #wp-esig-notification-overlay').on('click', function() {
                    $('#wp-esig-success-notification, #wp-esig-notification-overlay').fadeOut(300, function() {
                        $(this).remove();
                    });
                });

                // 3秒后自动关闭
                setTimeout(function() {
                    $('#wp-esig-notification-close').trigger('click');
                }, 8000);
            }
        });
        </script>
        <?php
    }
}
