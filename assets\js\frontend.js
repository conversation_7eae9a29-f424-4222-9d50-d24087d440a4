/**
 * WP Electronic Signature Frontend JavaScript
 */

(function($) {
    'use strict';
    
    var WPEsigFrontend = {
        
        // 签名画板对象
        signaturePad: null,
        modalSignaturePad: null,
        
        // 初始化
        init: function() {
            console.log('🚀 WP Electronic Signature Frontend 初始化开始...');
            console.log('📍 当前页面URL:', window.location.href);
            console.log('📍 wp_esig_ajax对象:', wp_esig_ajax);

            this.initSignaturePad();
            this.initModalSignaturePad();
            this.bindEvents();
            this.loadContractContent();
            this.initInlineSignature();

            console.log('✅ WP Electronic Signature Frontend 初始化完成');
        },
        
        // 初始化签名画板
        initSignaturePad: function() {
            // 检查SignaturePad库是否已加载
            if (typeof SignaturePad === 'undefined') {
                console.error('SignaturePad库未加载！');
                return;
            }

            var canvas = document.getElementById('wp-esig-signature-pad');
            if (canvas) {
                // 延迟初始化，确保canvas已经正确渲染
                var self = this;
                setTimeout(function() {
                    try {
                        self.signaturePad = new SignaturePad(canvas, {
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            penColor: 'rgb(0, 0, 0)'
                        });

                        // 调整画板大小
                        self.resizeCanvas(canvas);
                    } catch (error) {
                        console.error('初始化签名画板失败:', error);
                    }
                }, 100);
            }
        },
        
        // 初始化弹窗签名画板
        initModalSignaturePad: function() {
            console.log('正在初始化弹窗签名画板...');

            // 检查SignaturePad库是否已加载
            if (typeof SignaturePad === 'undefined') {
                console.error('SignaturePad库未加载！');
                return;
            }

            var canvas = document.getElementById('wp-esig-modal-signature-pad');
            if (canvas) {
                console.log('找到弹窗签名画板canvas元素');
                try {
                    // 直接显示画板，不需要隐藏
                    canvas.style.display = 'block';
                    canvas.style.border = '2px solid #007cba';
                    canvas.style.borderRadius = '5px';
                    canvas.style.backgroundColor = 'transparent';

                    this.modalSignaturePad = new SignaturePad(canvas, {
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        penColor: 'rgb(0, 0, 0)',
                        minWidth: 1,
                        maxWidth: 3
                    });

                    // 添加签名画板事件监听器
                    var self = this;
                    this.modalSignaturePad.addEventListener('beginStroke', function() {
                        // 用户开始签名时更新验证状态
                        setTimeout(function() {
                            self.updateModalConfirmButton();
                        }, 100);
                    });

                    this.modalSignaturePad.addEventListener('endStroke', function() {
                        // 用户结束签名时更新验证状态
                        setTimeout(function() {
                            self.updateModalConfirmButton();
                        }, 100);
                    });

                    // 调整画板大小
                    this.resizeCanvas(canvas);
                    console.log('弹窗签名画板初始化完成');
                } catch (error) {
                    console.error('初始化弹窗签名画板失败:', error);
                }
            } else {
                console.log('未找到弹窗签名画板canvas元素');
            }
        },
        
        // 调整画板大小
        resizeCanvas: function(canvas) {
            if (!canvas) {
                console.log('resizeCanvas: canvas为空');
                return;
            }

            console.log('=== 开始调整Canvas大小 ===');
            var rect = canvas.getBoundingClientRect();
            var ratio = Math.max(window.devicePixelRatio || 1, 1);

            console.log('Canvas getBoundingClientRect:', rect);
            console.log('设备像素比:', ratio);

            // 确保canvas有最小尺寸
            var width = Math.max(rect.width, 400);
            var height = Math.max(rect.height, 150);

            console.log('调整后尺寸:', width, 'x', height);

            // 设置实际大小
            canvas.width = width * ratio;
            canvas.height = height * ratio;

            // 设置显示大小
            canvas.style.width = width + 'px';
            canvas.style.height = height + 'px';

            // 缩放绘图上下文
            var ctx = canvas.getContext('2d');
            ctx.scale(ratio, ratio);

            // 重新设置绘图样式
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';

            // 不设置任何背景色，保持透明背景

            console.log('Canvas最终尺寸:', canvas.width, 'x', canvas.height);
            console.log('=== Canvas大小调整完成 ===');
        },
        
        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 清除签名按钮
            $('#wp-esig-clear-signature').on('click', function() {
                self.clearSignature();
            });
            
            $('#wp-esig-modal-clear-signature').on('click', function() {
                self.clearModalSignature();
            });
            
            // 保存签名按钮
            $('#wp-esig-save-signature').on('click', function() {
                self.saveSignature();
            });
            
            $('#wp-esig-modal-save-signature').on('click', function() {
                self.saveModalSignature();
            });

            // 移除占位符和预览的点击事件，因为已经直接显示签名画板
            
            // 合同中的复选框已移除，只保留弹窗中的复选框
            // $('#wp-esig-agree-contract').on('change', function() {
            //     self.updateSubmitButton();
            // });
            
            $('#wp-esig-modal-agree-contract').on('change', function() {
                self.updateModalConfirmButton();
            });

            // 测试PDF下载按钮
            $(document).on('click', '#wp-esig-test-pdf-download', function() {
                self.generatePdfFromImage();
            });

            // CPF输入字段事件
            $(document).on('input', '#wp-esig-modal-cpf', function() {
                var cpf = $(this).val();
                $(this).val(self.formatCPF(cpf));
                self.updateModalConfirmButton();

                // 实时更新合同中的CPF显示
                self.updateContractCPF(cpf);
            });

            // 买家邮箱输入字段事件 - 统一到frontend.js中处理
            $(document).on('input', '#wp-esig-modal-buyer-email', function() {
                var emailValue = $(this).val();
                console.log('邮箱输入框值变化:', emailValue);

                // 实时保存邮箱到会话
                if (emailValue && emailValue.trim() !== '') {
                    $.ajax({
                        url: wp_esig_ajax.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'save_custom_email_data',
                            nonce: wp_esig_ajax.nonce,
                            custom_email: emailValue
                        },
                        success: function(response) {
                            if (response.success) {
                                console.log('自定义邮箱保存成功:', response.data.custom_email);
                            } else {
                                console.log('自定义邮箱保存失败:', response.data);
                            }
                        },
                        error: function() {
                            console.log('自定义邮箱保存请求失败');
                        }
                    });
                }

                // 实时更新合同中的自定义邮件显示
                self.updateContractCustomEmail(emailValue);

                // 触发确认按钮状态更新（如果函数存在）
                if (typeof updateModalConfirmButton === 'function') {
                    updateModalConfirmButton();
                }
            });

            // 移除卖家邮箱输入字段事件（现在使用后台设置的邮箱）

            // 窗口大小改变时重新调整画板
            $(window).on('resize', function() {
                if (self.signaturePad) {
                    self.resizeCanvas(document.getElementById('wp-esig-signature-pad'));
                }
                if (self.modalSignaturePad) {
                    self.resizeCanvas(document.getElementById('wp-esig-modal-signature-pad'));
                }
            });
            
            // 阻止结账表单提交（如果需要签名）
            $('form.checkout').on('submit', function(e) {
                if (!self.hasValidSignature()) {
                    e.preventDefault();
                    alert(wp_esig_ajax.messages.signature_required);
                    return false;
                }
            });
        },
        
        // 加载合同内容
        loadContractContent: function() {
            var self = this;

            // 获取客户信息
            var customerData = this.getCustomerData();

            console.log('🔄 开始加载合同内容...');
            console.log('🌐 AJAX URL:', wp_esig_ajax.ajax_url);
            console.log('👤 客户数据:', customerData);
            console.log('🔐 Nonce:', wp_esig_ajax.nonce);
            console.log('⏰ 当前时间:', new Date().toLocaleString());

            // 检查必要的对象是否存在
            if (typeof wp_esig_ajax === 'undefined') {
                console.error('❌ wp_esig_ajax 对象未定义！');
                return;
            }
            if (!wp_esig_ajax.ajax_url) {
                console.error('❌ AJAX URL 未设置！');
                return;
            }
            if (!wp_esig_ajax.nonce) {
                console.error('❌ Nonce 未设置！');
                return;
            }

            $.ajax({
                url: wp_esig_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_contract_content',
                    nonce: wp_esig_ajax.nonce,
                    customer_name: customerData.name,
                    customer_email: customerData.email,
                    customer_phone: customerData.phone
                },
                timeout: 30000, // 30秒超时
                success: function(response) {
                    console.log('AJAX响应:', response);

                    if (response.success) {
                        // 调试：检查返回的内容是否包含签名区域
                        console.log('合同内容加载成功');
                        console.log('内容长度:', response.data.content.length);
                        console.log('是否包含签名区域:', response.data.content.includes('wp-esig-inline-signatures'));
                        console.log('是否包含VENDEDOR:', response.data.content.includes('VENDEDOR'));
                        console.log('是否包含COMPRADOR:', response.data.content.includes('COMPRADOR'));

                        // 显示调试信息
                        if (response.data.debug) {
                            console.log('调试信息:', response.data.debug);
                            console.log('模板哈希:', response.data.debug.template_hash);
                            console.log('获取时间:', response.data.debug.timestamp);
                        }

                        $('#wp-esig-contract-content').html(response.data.content);
                        $('#wp-esig-modal-contract-content').html(response.data.content);

                        // 添加调试按钮（仅在开发模式下显示）
                        if (window.location.search.includes('debug=1')) {
                            var debugButton = '<div style="position: fixed; top: 10px; right: 10px; z-index: 9999;">' +
                                '<button onclick="WPEsigFrontend.loadContractContent()" style="background: #007cba; color: white; border: none; padding: 5px 10px; border-radius: 3px; font-size: 12px;">刷新合同</button>' +
                                '</div>';
                            $('body').append(debugButton);
                        }

                        // 在弹窗模式下调整签名区域显示
                        self.adjustModalSignatureArea();
                        
                        // 延迟再次清理，确保没有其他脚本重新添加提示文本
                        setTimeout(function() {
                            self.forceCleanModalSignature();
                        }, 1000);

                        // 不再添加同意复选框到合同内容中，只使用弹窗中的复选框
                        // self.addAgreementCheckbox();

                        // 初始化签名区域交互
                        self.initSignatureAreaInteraction();

                        // 如果CPF字段已有值，更新合同显示
                        setTimeout(function() {
                            var existingCpf = $('#wp-esig-modal-cpf').val();
                            if (existingCpf) {
                                self.updateContractCPF(existingCpf);
                            }

                            // 如果邮件字段已有值，更新合同显示
                            var existingEmail = $('#wp-esig-modal-buyer-email').val();
                            if (existingEmail) {
                                self.updateContractCustomEmail(existingEmail);
                            }
                        }, 500);
                    } else {
                        console.error('合同内容加载失败:', response);
                        var errorMessage = response.data || wp_esig_ajax.messages.signature_error;
                        var errorHtml = '<div class="wp-esig-error" style="padding: 20px; background: #ffebee; border: 1px solid #f44336; border-radius: 5px; color: #c62828; text-align: center;">' +
                            '<h4 style="margin: 0 0 10px 0;">❌ Erro ao carregar contrato</h4>' +
                            '<p style="margin: 0;">' + errorMessage + '</p>' +
                            '<p style="margin: 10px 0 0 0; font-size: 12px; color: #666;">Por favor, recarregue a página ou entre em contato com o suporte.</p>' +
                            '</div>';
                        $('#wp-esig-contract-content').html(errorHtml);
                        $('#wp-esig-modal-contract-content').html(errorHtml);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('🚨 AJAX请求失败 - 详细信息:');
                    console.error('📊 状态码:', xhr.status);
                    console.error('📊 状态文本:', status);
                    console.error('📊 错误信息:', error);
                    console.error('📊 完整XHR对象:', xhr);
                    console.error('📊 响应文本:', xhr.responseText);
                    console.error('📊 请求URL:', wp_esig_ajax.ajax_url);
                    console.error('📊 请求数据:', {
                        action: 'get_contract_content',
                        nonce: wp_esig_ajax.nonce
                    });

                    var errorDetails = '';
                    if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.data) {
                                errorDetails = '<p style="margin: 10px 0; font-size: 12px; color: #666;">Detalhes: ' + response.data + '</p>';
                            }
                        } catch (e) {
                            errorDetails = '<p style="margin: 10px 0; font-size: 12px; color: #666;">Resposta do servidor: ' + xhr.responseText.substring(0, 200) + '</p>';
                        }
                    }

                    var errorHtml = '<div class="wp-esig-error" style="padding: 20px; background: #ffebee; border: 1px solid #f44336; border-radius: 5px; color: #c62828; text-align: center;">' +
                        '<h4 style="margin: 0 0 10px 0;">❌ Erro de conexão</h4>' +
                        '<p style="margin: 0;">Não foi possível carregar o contrato. Verifique sua conexão com a internet.</p>' +
                        '<p style="margin: 10px 0 0 0; font-size: 12px; color: #666;">Código do erro: ' + status + '</p>' +
                        errorDetails +
                        '<button onclick="location.reload()" style="margin-top: 15px; padding: 8px 16px; background: #f44336; color: white; border: none; border-radius: 3px; cursor: pointer;">Recarregar página</button>' +
                        '</div>';
                    $('#wp-esig-contract-content').html(errorHtml);
                    $('#wp-esig-modal-contract-content').html(errorHtml);
                }
            });
        },
        
        // 获取客户数据
        getCustomerData: function() {
            return {
                name: $('#billing_first_name').val() + ' ' + $('#billing_last_name').val(),
                email: $('#billing_email').val(),
                phone: $('#billing_phone').val()
            };
        },
        
        // 清除签名
        clearSignature: function() {
            if (this.signaturePad) {
                this.signaturePad.clear();
                $('#wp-esig-signature-preview').hide();
                this.updateSubmitButton();
            }
        },
        
        // 清除弹窗签名
        clearModalSignature: function() {
            if (this.modalSignaturePad) {
                this.modalSignaturePad.clear();
            }

            // 清除合同中的签名
            this.clearContractSignature();

            // 更新确认按钮状态
            this.updateModalConfirmButton();

            console.log('签名已清除');
        },
        
        // 保存签名
        saveSignature: function() {
            if (!this.signaturePad || this.signaturePad.isEmpty()) {
                alert(wp_esig_ajax.messages.signature_required);
                return;
            }
            
            var signatureData = this.signaturePad.toDataURL('image/png');
            this.submitSignature(signatureData, '#wp-esig-signature-preview', '#wp-esig-signature-image');
        },
        
        // 保存弹窗签名
        saveModalSignature: function() {
            if (!this.modalSignaturePad || this.modalSignaturePad.isEmpty()) {
                alert('Por favor, faça sua assinatura primeiro');
                return;
            }

            var signatureData = this.modalSignaturePad.toDataURL('image/png');

            // 保存签名数据到会话
            this.submitSignature(signatureData, '#wp-esig-modal-signature-preview', '#wp-esig-modal-signature-image');

            // 直接更新合同中的签名区域，不显示预览
            this.updateContractSignature(signatureData);

            // 更新确认按钮状态
            this.updateModalConfirmButton();

            console.log('签名保存成功');
        },
        
        // 提交签名
        submitSignature: function(signatureData, previewSelector, imageSelector) {
            var self = this;
            
            $.ajax({
                url: wp_esig_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'save_signature',
                    nonce: wp_esig_ajax.nonce,
                    signature_data: signatureData
                },
                success: function(response) {
                    if (response.success) {
                        $(imageSelector).attr('src', response.data.image_url);
                        $(previewSelector).show();
                        self.updateSubmitButton();
                        self.updateModalConfirmButton();
                        // 移除签名保存成功的弹窗提示
                        // alert(wp_esig_ajax.messages.signature_saved);
                    } else {
                        alert(response.data || wp_esig_ajax.messages.signature_error);
                    }
                },
                error: function() {
                    alert(wp_esig_ajax.messages.signature_error);
                }
            });
        },
        
        // 检查是否有有效签名
        hasValidSignature: function() {
            return $('#wp-esig-signature-preview').is(':visible') || $('#wp-esig-modal-signature-preview').is(':visible');
        },
        
        // 更新提交按钮状态
        updateSubmitButton: function() {
            // 弹窗模式下不需要更新提交按钮状态，由弹窗逻辑控制
        },
        
        // 更新弹窗确认按钮状态
        updateModalConfirmButton: function() {
            // 检查签名是否完成（检查画板是否有内容，而不是预览元素）
            var hasSignature = this.modalSignaturePad && !this.modalSignaturePad.isEmpty();

            // 检查同意条款
            var hasAgreed = $('#wp-esig-modal-agree-contract').is(':checked');

            // 检查CPF
            var cpfValue = $('#wp-esig-modal-cpf').val().replace(/\D/g, '');
            var hasValidCpf = cpfValue.length === 11 && this.isValidCPF(cpfValue);

            // 所有条件都满足才启用确认按钮（移除卖家邮箱验证）
            var allValid = hasSignature && hasAgreed && hasValidCpf;
            $('#wp-esig-modal-confirm').prop('disabled', !allValid);

            // 显示CPF错误信息
            if (cpfValue.length > 0 && !hasValidCpf) {
                $('#wp-esig-cpf-error').text('CPF inválido').show();
            } else {
                $('#wp-esig-cpf-error').hide();
            }

            // 显示综合错误提示
            this.showValidationErrors(hasSignature, hasValidCpf, hasAgreed);

            console.log('确认按钮状态更新:', {
                hasSignature: hasSignature,
                hasAgreed: hasAgreed,
                hasValidCpf: hasValidCpf,
                disabled: !allValid
            });
        },

        // 捕获合同图片 - 使用iframe隔离方案避免主题冲突
        captureContractImage: function() {
            var self = this;
            console.log('🖼️ 开始捕获合同图片（iframe隔离方案）...');

            return new Promise(function(resolve, reject) {
                // 检查html2canvas是否已加载
                if (typeof html2canvas === 'undefined') {
                    console.error('❌ html2canvas库未加载！');
                    reject('html2canvas库未加载');
                    return;
                }

                // 获取合同内容区域
                var contractElement = document.getElementById('wp-esig-modal-contract-content');
                if (!contractElement) {
                    console.error('❌ 找不到合同内容元素');
                    reject('合同内容元素不存在');
                    return;
                }

                // 确保元素已完全渲染
                if (contractElement.offsetWidth === 0 || contractElement.offsetHeight === 0) {
                    console.error('❌ 合同元素尺寸为0，可能未完全加载');
                    reject('合同元素未完全加载');
                    return;
                }

                console.log('📏 原始元素尺寸:', contractElement.offsetWidth + 'x' + contractElement.offsetHeight);

                // 创建隔离的iframe
                var iframe = document.createElement('iframe');
                iframe.style.position = 'absolute';
                iframe.style.left = '-9999px';
                iframe.style.top = '-9999px';
                iframe.style.width = contractElement.offsetWidth + 'px';
                iframe.style.height = contractElement.offsetHeight + 'px';
                iframe.style.border = 'none';
                iframe.style.visibility = 'hidden';

                document.body.appendChild(iframe);

                // 等待iframe创建完成
                setTimeout(function() {
                    try {
                        // 获取合同内容的HTML
                        var contractHTML = contractElement.innerHTML;

                        // 获取相关的CSS样式
                        var styles = self.extractContractStyles();

                        // 构建完整的HTML文档
                        var iframeContent = `
                            <!DOCTYPE html>
                            <html>
                            <head>
                                <meta charset="utf-8">
                                <style>
                                    body {
                                        margin: 0;
                                        padding: 20px;
                                        font-family: Arial, sans-serif;
                                        background: #ffffff;
                                        width: ${contractElement.offsetWidth - 40}px;
                                    }
                                    ${styles}
                                </style>
                            </head>
                            <body>
                                ${contractHTML}
                            </body>
                            </html>
                        `;

                        // 写入iframe内容
                        var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        iframeDoc.open();
                        iframeDoc.write(iframeContent);
                        iframeDoc.close();

                        console.log('📄 iframe内容已写入');

                        // 等待iframe内容完全加载
                        setTimeout(function() {
                            var iframeBody = iframeDoc.body;

                            console.log('📏 iframe body尺寸:', iframeBody.offsetWidth + 'x' + iframeBody.offsetHeight);

                            // html2canvas配置
                            var options = {
                                backgroundColor: '#ffffff',
                                scale: 1,
                                useCORS: false,
                                allowTaint: false,
                                logging: false
                            };

                            // 对iframe内容进行截图
                            html2canvas(iframeBody, options)
                                .then(function(canvas) {
                                    console.log('✅ 合同截图成功（iframe隔离）');
                                    console.log('📏 canvas尺寸:', canvas.width + 'x' + canvas.height);

                                    // 转换为base64
                                    var imageData = canvas.toDataURL('image/png', 0.95);
                                    console.log('📊 图片数据大小:', Math.round(imageData.length / 1024) + 'KB');

                                    // 清理iframe
                                    if (iframe && iframe.parentNode) {
                                        iframe.parentNode.removeChild(iframe);
                                        console.log('🧹 已清理iframe');
                                    }

                                    resolve(imageData);
                                })
                                .catch(function(error) {
                                    console.error('❌ iframe截图失败:', error);

                                    // 清理iframe
                                    if (iframe && iframe.parentNode) {
                                        iframe.parentNode.removeChild(iframe);
                                        console.log('🧹 已清理iframe（错误情况）');
                                    }

                                    reject('截图失败: ' + (error.message || error));
                                });
                        }, 500); // 等待500ms确保iframe内容完全渲染

                    } catch (error) {
                        console.error('❌ iframe创建异常:', error);

                        // 清理iframe
                        if (iframe && iframe.parentNode) {
                            iframe.parentNode.removeChild(iframe);
                        }

                        reject('iframe创建异常: ' + error.message);
                    }
                }, 100); // 等待100ms确保DOM稳定
            });
        },

        // 提取合同相关的CSS样式
        extractContractStyles: function() {
            var styles = `
                /* 基础样式 */
                * {
                    box-sizing: border-box;
                }

                /* 合同内容样式 */
                .wp-esig-contract-content {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 40px;
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                }

                /* 标题样式 */
                h1, h2, h3, h4, h5, h6 {
                    margin: 20px 0 10px 0;
                    font-weight: bold;
                }

                h1 {
                    font-size: 24px;
                    text-align: center;
                    border-bottom: 2px solid #333;
                    padding-bottom: 10px;
                    margin-bottom: 30px;
                }

                h3 {
                    font-size: 16px;
                    margin: 25px 0 15px 0;
                }

                /* 段落样式 */
                p {
                    margin: 8px 0;
                }

                /* 强调文本 */
                strong {
                    font-weight: bold;
                }

                /* 背景区域 */
                div[style*="background"] {
                    background: #f9f9f9 !important;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }

                /* 签名区域 */
                div[style*="ASSINATURAS"] {
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                }

                /* 表格样式 */
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 10px 0;
                }

                td, th {
                    padding: 8px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }

                /* 隐藏不需要的元素 */
                script, .wp-esig-signature-controls, .wp-esig-modal-close {
                    display: none !important;
                }
            `;

            return styles;
        },

        // 生成PDF从图片
        generatePdfFromImage: function() {
            var self = this;

            console.log('📄 开始生成PDF...');

            // 显示加载提示
            var $confirmButton = $('#wp-esig-modal-confirm');
            var originalText = $confirmButton.text();
            $confirmButton.text('Gerando PDF...').prop('disabled', true);

            // 先捕获合同图片
            this.captureContractImage()
                .then(function(contractImageData) {
                    // 获取其他必要数据
                    var cpfValue = $('#wp-esig-modal-cpf').val();
                    var buyerEmail = $('#wp-esig-modal-buyer-email').val();
                    var customerData = self.getCustomerData();

                    console.log('📤 准备发送PDF生成请求...');

                    // 发送到后端生成PDF
                    $.ajax({
                        url: wp_esig_ajax.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'generate_pdf_from_image',
                            nonce: wp_esig_ajax.nonce,
                            contract_image: contractImageData,
                            cpf: cpfValue,
                            buyer_email: buyerEmail,
                            customer_name: (customerData.billing_first_name + ' ' + customerData.billing_last_name).trim() || 'Cliente',
                            customer_email: buyerEmail,
                            customer_phone: customerData.billing_phone || ''
                        },
                        success: function(response) {
                            // 恢复按钮状态
                            $confirmButton.text(originalText).prop('disabled', false);

                            if (response.success) {
                                console.log('✅ PDF生成成功:', response.data);

                                // 下载PDF
                                if (response.data.download_url) {
                                    // 创建下载链接
                                    var link = document.createElement('a');
                                    link.href = response.data.download_url;
                                    link.download = 'contrato-eletronico.pdf';
                                    document.body.appendChild(link);
                                    link.click();
                                    document.body.removeChild(link);

                                    // 显示成功消息
                                    alert('PDF gerado com sucesso! Baixando...');
                                }
                            } else {
                                console.error('❌ PDF生成失败:', response.data);
                                alert('Falha ao gerar PDF: ' + (response.data || 'Erro desconhecido'));
                            }
                        },
                        error: function(xhr, status, error) {
                            // 恢复按钮状态
                            $confirmButton.text(originalText).prop('disabled', false);

                            console.error('❌ PDF生成请求失败:', error);
                            alert('Falha na solicitação de geração de PDF, tente novamente');
                        }
                    });
                })
                .catch(function(error) {
                    // 恢复按钮状态
                    $confirmButton.text(originalText).prop('disabled', false);

                    console.error('❌ 合同截图失败:', error);
                    alert('Falha na captura do contrato, tente novamente');
                });
        },

        // CPF验证函数
        isValidCPF: function(cpf) {
            if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) return false;

            var sum = 0;
            for (var i = 0; i < 9; i++) {
                sum += parseInt(cpf.charAt(i)) * (10 - i);
            }
            var remainder = (sum * 10) % 11;
            if (remainder === 10 || remainder === 11) remainder = 0;
            if (remainder !== parseInt(cpf.charAt(9))) return false;

            sum = 0;
            for (var i = 0; i < 10; i++) {
                sum += parseInt(cpf.charAt(i)) * (11 - i);
            }
            remainder = (sum * 10) % 11;
            if (remainder === 10 || remainder === 11) remainder = 0;
            return remainder === parseInt(cpf.charAt(10));
        },

        // 邮箱验证函数
        isValidEmail: function(email) {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        // 显示综合验证错误提示
        showValidationErrors: function(hasSignature, hasValidCpf, hasAgreed) {
            var errors = [];

            if (!hasSignature) {
                errors.push('Assinatura');
            }
            if (!hasValidCpf) {
                errors.push('CPF');
            }
            if (!hasAgreed) {
                errors.push('Concordar com os termos');
            }

            // 移除之前的错误提示
            $('#wp-esig-general-error').remove();

            if (errors.length > 0) {
                var errorMessage = 'Por favor, preencha os seguintes campos obrigatórios: ' + errors.join(', ');
                var errorHtml = '<div id="wp-esig-general-error" class="wp-esig-error-message" style="color: #d63638; margin-top: 10px; padding: 8px; background: #ffeaea; border: 1px solid #d63638; border-radius: 4px;">' + errorMessage + '</div>';
                $('#wp-esig-modal-confirm').after(errorHtml);
            }
        },

        // 格式化CPF
        formatCPF: function(cpf) {
            cpf = cpf.replace(/\D/g, ''); // 移除非数字字符
            cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
            cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
            cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            return cpf;
        },

        // 更新合同中的签名区域
        updateContractSignature: function(signatureData) {
            // 更新弹窗中合同的签名区域
            var buyerSignatureArea = $('#wp-esig-modal-contract-content').find('#wp-esig-buyer-signature-area');
            if (buyerSignatureArea.length > 0) {
                buyerSignatureArea.html(
                    '<img src="' + signatureData + '" alt="Assinatura do Cliente" style="max-width: 100%; max-height: 100%; object-fit: contain; background: transparent; border: none;">'
                );
            }
        },

        // 更新合同中的CPF显示
        updateContractCPF: function(cpfValue) {
            var modalContent = $('#wp-esig-modal-contract-content');
            var displayValue = cpfValue || '[A ser preenchido]';

            console.log('开始更新CPF显示:', cpfValue);

            // 方法1: 通过ID精确定位（最可靠的方法）
            var cpfDisplay = modalContent.find('#contract-cpf-display');
            if (cpfDisplay.length > 0) {
                cpfDisplay.text(displayValue);
                console.log('通过ID更新CPF成功');
            } else {
                console.log('未找到ID为contract-cpf-display的元素');
            }

            // 方法2: 精确查找CPF的span元素（只查找CPF相关的）
            modalContent.find('p').each(function() {
                var $p = $(this);
                var text = $p.text();
                // 只处理包含"CPF:"且不包含"Nome Completo:"的段落
                if (text && text.includes('CPF:') && !text.includes('Nome Completo:')) {
                    var $span = $p.find('span#contract-cpf-display');
                    if ($span.length > 0) {
                        $span.text(displayValue);
                        console.log('通过精确查找更新CPF成功');
                    }
                }
            });

            console.log('CPF更新完成:', displayValue);
        },

        // 更新合同中的自定义邮件显示
        updateContractCustomEmail: function(emailValue) {
            var modalContent = $('#wp-esig-modal-contract-content');
            var displayValue = emailValue || '[A ser preenchido]';

            console.log('=== 开始更新自定义邮件显示 ===');
            console.log('输入的邮件值:', emailValue);
            console.log('显示值:', displayValue);
            console.log('模态内容元素数量:', modalContent.length);

            // 方法1: 通过ID精确定位（最可靠的方法）
            var emailDisplay = modalContent.find('#contract-custom-email-display');
            console.log('找到的邮件显示元素数量:', emailDisplay.length);
            if (emailDisplay.length > 0) {
                console.log('元素当前内容:', emailDisplay.text());
                emailDisplay.text(displayValue);
                console.log('通过ID更新自定义邮件成功，新内容:', emailDisplay.text());
            } else {
                console.log('未找到ID为contract-custom-email-display的元素');
            }

            // 方法2: 精确查找邮箱的span元素（只查找邮箱相关的）
            modalContent.find('p').each(function() {
                var $p = $(this);
                var text = $p.text();
                // 只处理包含"Nome Completo:"且不包含"CPF:"的段落
                if (text && text.includes('Nome Completo:') && !text.includes('CPF:')) {
                    var $span = $p.find('span#contract-custom-email-display');
                    if ($span.length > 0) {
                        $span.text(displayValue);
                        console.log('通过精确查找更新自定义邮件成功');
                    }
                }
            });

            console.log('自定义邮件更新完成:', displayValue);
            console.log('=== 自定义邮件显示更新结束 ===');
        },

        // 调试函数：检查邮箱变量状态
        debugCustomEmailStatus: function() {
            console.log('=== 调试：自定义邮箱状态检查 ===');

            // 检查输入框
            var emailInput = $('#wp-esig-modal-buyer-email');
            console.log('邮箱输入框存在:', emailInput.length > 0);
            console.log('邮箱输入框值:', emailInput.val());

            // 检查显示元素
            var emailDisplay = $('#contract-custom-email-display');
            console.log('邮箱显示元素存在:', emailDisplay.length > 0);
            console.log('邮箱显示元素内容:', emailDisplay.text());

            // 检查会话数据
            $.ajax({
                url: wp_esig_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_contract_content',
                    nonce: wp_esig_ajax.nonce
                },
                success: function(response) {
                    console.log('会话中的邮箱数据:', response);
                }
            });

            console.log('=== 调试检查完成 ===');
        },

        // 显示签名画板（已简化，画板直接显示）
        showSignaturePad: function() {
            console.log('签名画板已直接显示，无需额外操作');
        },

        // 隐藏签名画板（已简化，不再需要）
        hideSignaturePad: function() {
            console.log('签名画板保持显示，无需隐藏');
        },

        // 清除合同中的签名
        clearContractSignature: function() {
            var buyerSignatureArea = $('#wp-esig-modal-contract-content').find('#wp-esig-buyer-signature-area');
            if (buyerSignatureArea.length > 0) {
                buyerSignatureArea.html('');
            }
            console.log('合同签名已清除');
        },

        // 初始化内联签名功能
        initInlineSignature: function() {
            var self = this;

            // 点击签名区域显示签名画板
            $('#wp-esig-buyer-signature-area').on('click', function() {
                if (!$('#wp-esig-signature-pad').is(':visible')) {
                    self.showInlineSignaturePad();
                }
            });

            // 清除签名
            $('#wp-esig-clear-signature').on('click', function() {
                if (self.signaturePad) {
                    self.signaturePad.clear();
                    $('#wp-esig-signature-preview').hide();
                    $('#wp-esig-signature-placeholder').show();
                }
            });

            // 保存签名
            $('#wp-esig-save-signature').on('click', function() {
                if (self.signaturePad && !self.signaturePad.isEmpty()) {
                    var signatureData = self.signaturePad.toDataURL('image/png');
                    $('#wp-esig-signature-image').attr('src', signatureData);
                    $('#wp-esig-signature-preview').show();
                    $('#wp-esig-signature-pad').hide();
                    $('#wp-esig-signature-controls').hide();
                    $('#wp-esig-signature-placeholder').hide();

                    // 启用结账按钮
                    $('.woocommerce-checkout .button[type="submit"]').prop('disabled', false);

                    // 保存签名数据到隐藏字段
                    if ($('#wp-esig-signature-data').length === 0) {
                        $('<input>').attr({
                            type: 'hidden',
                            id: 'wp-esig-signature-data',
                            name: 'wp_esig_signature_data',
                            value: signatureData
                        }).appendTo('form.checkout');
                    } else {
                        $('#wp-esig-signature-data').val(signatureData);
                    }
                } else {
                    alert('Por favor, faça sua assinatura primeiro');
                }
            });
        },

        // 显示内联签名画板
        showInlineSignaturePad: function() {
            $('#wp-esig-signature-placeholder').hide();
            $('#wp-esig-signature-pad').show();
            $('#wp-esig-signature-controls').show();

            // 重新初始化签名画板
            var canvas = document.getElementById('wp-esig-signature-pad');
            if (canvas) {
                // 如果已经存在签名画板，先清理
                if (this.signaturePad) {
                    this.signaturePad.clear();
                } else {
                    // 检查SignaturePad库是否已加载
                    if (typeof SignaturePad === 'undefined') {
                        console.error('SignaturePad库未加载！');
                        return;
                    }

                    try {
                        // 创建新的签名画板
                        this.signaturePad = new SignaturePad(canvas, {
                            backgroundColor: 'rgba(255, 255, 255, 1)',
                            penColor: 'rgb(0, 0, 0)'
                        });
                    } catch (error) {
                        console.error('创建内联签名画板失败:', error);
                        return;
                    }
                }
                this.resizeCanvas(canvas);

                // 确保canvas可以接收事件
                canvas.style.pointerEvents = 'auto';
                canvas.style.touchAction = 'none';
            }

            // 禁用结账按钮直到签名完成
            $('.woocommerce-checkout .button[type="submit"]').prop('disabled', true);
        },

        // 调整弹窗模式下的签名区域显示
        adjustModalSignatureArea: function() {
            var modalContent = $('#wp-esig-modal-contract-content');

            // 调试：检查弹窗中的签名区域
            console.log('调整弹窗签名区域显示');
            console.log('弹窗内容元素数量:', modalContent.length);
            console.log('签名区域元素数量:', modalContent.find('.wp-esig-inline-signatures').length);

            // 确保签名区域可见
            var signatureAreas = modalContent.find('.wp-esig-inline-signatures');
            signatureAreas.show();
            console.log('显示签名区域，元素数量:', signatureAreas.length);

            // 处理买家签名区域，保留后台修改的内容
            var buyerSignatureArea = modalContent.find('#wp-esig-buyer-signature-area');
            if (buyerSignatureArea.length > 0) {
                console.log('找到买家签名区域，保留原始内容并移除交互元素');

                // 保存原始内容（来自后台模板）
                var originalContent = buyerSignatureArea.html();
                console.log('原始签名区域内容:', originalContent);

                // 只移除前端添加的交互元素，保留后台设置的内容
                buyerSignatureArea.find('#wp-esig-signature-placeholder').remove();
                buyerSignatureArea.find('#wp-esig-signature-pad').remove();
                buyerSignatureArea.find('#wp-esig-signature-controls').remove();
                buyerSignatureArea.find('#wp-esig-signature-preview').remove();

                // 移除其他可能的占位符元素
                modalContent.find('#wp-esig-signature-placeholder').remove();

                // 移除所有事件监听器和交互功能，但保留内容
                buyerSignatureArea.off();
                buyerSignatureArea.css({
                    'cursor': 'default',
                    'pointer-events': 'none'
                });

                console.log('签名区域处理完成，保留了后台设置的内容');
            } else {
                console.log('未找到买家签名区域元素');
            }
        },

        // 强制清理弹窗签名区域（只清理前端添加的元素）
        forceCleanModalSignature: function() {
            var modalContent = $('#wp-esig-modal-contract-content');

            // 只移除前端动态添加的占位符元素，不影响后台设置的内容
            modalContent.find('#wp-esig-signature-placeholder').remove();

            // 移除前端添加的交互元素，但保留后台模板中的原始内容
            modalContent.find('#wp-esig-buyer-signature-area').each(function() {
                var $this = $(this);
                // 只移除前端添加的特定元素
                $this.find('#wp-esig-signature-pad').remove();
                $this.find('#wp-esig-signature-controls').remove();
                $this.find('#wp-esig-signature-preview').remove();

                // 不再清空整个区域，保留后台设置的内容
                console.log('保留签名区域的后台设置内容');
            });

            console.log('清理弹窗签名区域完成，保留了后台内容');
        },

        // 添加同意复选框
        addAgreementCheckbox: function() {
            // 查找签名区域
            var signatureArea = $('#wp-esig-modal-contract-content').find('div[style*="ASSINATURAS"]').parent();
            if (signatureArea.length === 0) {
                signatureArea = $('#wp-esig-modal-contract-content');
            }

            // 在签名区域之前添加同意复选框
            var agreementHtml = '<div class="wp-esig-agreement" style="margin: 30px 0; padding: 20px; background: #f0f8ff; border: 1px solid #007cba; border-radius: 5px; text-align: center;">' +
                '<label style="display: flex; align-items: center; justify-content: center; font-weight: 500; cursor: pointer;">' +
                '<input type="checkbox" id="wp-esig-agree-contract" required style="margin-right: 10px; transform: scale(1.2);">' +
                '<span>Li e concordo com todos os termos do contrato acima</span>' +
                '</label>' +
                '</div>';

            // 查找签名区域并在其前面插入
            var signatureSection = $('#wp-esig-modal-contract-content').find('div[style*="ASSINATURAS"]');
            if (signatureSection.length > 0) {
                signatureSection.before(agreementHtml);
            } else {
                $('#wp-esig-modal-contract-content').append(agreementHtml);
            }
        },

        // 初始化签名区域交互
        initSignatureAreaInteraction: function() {
            var self = this;

            // 合同中的复选框已移除，提交按钮状态由弹窗中的复选框控制
            // $(document).on('change', '#wp-esig-agree-contract', function() {
            //     var isChecked = $(this).is(':checked');
            //     var hasSignature = $('#wp-esig-signature-preview').is(':visible');
            //
            //     // 只有同意条款且完成签名才能提交
            //     if (isChecked && hasSignature) {
            //         $('.woocommerce-checkout .button[type="submit"]').prop('disabled', false);
            //     } else {
            //         $('.woocommerce-checkout .button[type="submit"]').prop('disabled', true);
            //     }
            // });

            // 修复移动设备上的布局问题
            this.fixMobileLayout();

            // 确保签名区域可见
            setTimeout(function() {
                var buyerSignatureArea = $('#wp-esig-buyer-signature-area');
                if (buyerSignatureArea.length > 0) {
                    // 滚动到签名区域
                    $('html, body').animate({
                        scrollTop: buyerSignatureArea.offset().top - 100
                    }, 1000);
                }
            }, 500);
        },

        // 修复移动设备布局
        fixMobileLayout: function() {
            if (window.innerWidth <= 768) {
                // 查找所有绝对定位的签名框
                $('.wp-esig-contract-content div[style*="position: absolute"]').each(function() {
                    var $this = $(this);
                    var style = $this.attr('style');

                    // 移除绝对定位相关样式
                    style = style.replace(/position:\s*absolute[^;]*;?/gi, '');
                    style = style.replace(/bottom:\s*[^;]*;?/gi, '');
                    style = style.replace(/left:\s*[^;]*;?/gi, '');
                    style = style.replace(/right:\s*[^;]*;?/gi, '');
                    style = style.replace(/width:\s*45%[^;]*;?/gi, 'width: 100%;');

                    // 添加移动设备样式
                    style += ' display: block; margin-bottom: 20px; position: static;';

                    $this.attr('style', style);
                });

                // 修复容器高度
                $('.wp-esig-contract-content div[style*="min-height: 180px"]').each(function() {
                    var style = $(this).attr('style');
                    style = style.replace(/min-height:\s*180px[^;]*;?/gi, 'min-height: auto;');
                    style = style.replace(/position:\s*relative[^;]*;?/gi, 'position: static;');
                    $(this).attr('style', style);
                });
            }
        }
    };
    
    // 页面加载完成后初始化
    $(document).ready(function() {
        WPEsigFrontend.init();
    });

    // 暴露到全局作用域，供弹窗使用
    window.WPEsigFrontend = WPEsigFrontend;

})(jQuery);
